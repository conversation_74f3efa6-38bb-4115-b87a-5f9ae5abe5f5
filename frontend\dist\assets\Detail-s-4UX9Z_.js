import{c as C,l as I,i as B,a as l,b as a,f as n,d as u,w as d,g,u as t,t as i,j as k,F as N,e as j,m as V,k as z,o as s,h as v}from"./index-BpqYk2C3.js";import{_ as E,a as L,u as M,L as R,E as S}from"./ErrorMessage-CVNw5bDd.js";const $={class:"detail-page"},F={class:"detail-header"},T={class:"container"},H={key:0,class:"header-info"},q={class:"gallery-title"},A={class:"gallery-meta"},J={class:"detail-content"},K={class:"container"},O={key:2,class:"gallery-detail"},P={class:"images-grid"},Q={class:"image-overlay"},U={class:"image-number"},W={key:0,class:"navigation-section"},X={key:0,class:"nav-item"},Y={class:"nav-content"},Z={class:"nav-info"},ee={key:1,class:"nav-item"},te={class:"nav-content"},ae={class:"nav-info"},re={__name:"Detail",setup(ne){const y=V(),h=z(),f=L(),e=M(),b=C(()=>{var r;const o=(r=e.currentGallery)==null?void 0:r.navigation;return o&&(o.prev||o.next)});async function m(){const o=parseInt(y.params.xrid);if(!o){f.error("无效的图库ID");return}try{await e.fetchGalleryDetail(o)}catch{f.error("加载详情失败")}}function D(){h.push({name:"Home"})}function x(o){h.push({name:"Detail",params:{xrid:o}})}return I(()=>y.params.xrid,()=>{y.name==="Detail"&&m()}),B(()=>{m()}),(o,r)=>{const w=g("n-button"),_=g("n-tag"),p=g("n-image"),G=g("n-card");return s(),l("div",$,[a("header",F,[a("div",T,[n(w,{onClick:D,type:"primary",ghost:""},{default:d(()=>r[2]||(r[2]=[v(" ← 返回首页 ",-1)])),_:1,__:[2]}),t(e).currentGallery?(s(),l("div",H,[a("h1",q,i(t(e).currentGallery.info.title),1),a("div",A,[n(_,{type:"info"},{default:d(()=>[v("ID: "+i(t(e).currentGallery.info.xrid),1)]),_:1}),n(_,{type:"success"},{default:d(()=>[v(i(t(e).currentGallery.images.length)+" 张图片",1)]),_:1})])])):u("",!0)])]),a("main",J,[a("div",K,[t(e).loading?(s(),k(R,{key:0,text:"加载详情中..."})):t(e).error?(s(),k(S,{key:1,title:t(e).error,description:"无法加载图片详情","show-back":"",onRetry:m},null,8,["title"])):t(e).currentGallery?(s(),l("div",O,[a("div",P,[(s(!0),l(N,null,j(t(e).currentGallery.images,(c,se)=>(s(),l("div",{key:c.id,class:"image-item"},[n(p,{src:c.reurl,alt:`图片 ${c.order}`,"object-fit":"cover","preview-src":c.reurl,"img-props":{loading:"lazy"},class:"gallery-image"},null,8,["src","alt","preview-src"]),a("div",Q,[a("span",U,i(c.order),1)])]))),128))]),b.value?(s(),l("div",W,[t(e).currentGallery.navigation.prev?(s(),l("div",X,[r[3]||(r[3]=a("h3",null,"上一套",-1)),n(G,{class:"nav-card",hoverable:"",onClick:r[0]||(r[0]=c=>x(t(e).currentGallery.navigation.prev.xrid))},{default:d(()=>[a("div",Y,[n(p,{src:t(e).currentGallery.navigation.prev.cover,alt:t(e).currentGallery.navigation.prev.title,"object-fit":"cover","preview-disabled":"",class:"nav-image"},null,8,["src","alt"]),a("div",Z,[a("h4",null,i(t(e).currentGallery.navigation.prev.title),1),n(_,{size:"small"},{default:d(()=>[v("ID: "+i(t(e).currentGallery.navigation.prev.xrid),1)]),_:1})])])]),_:1})])):u("",!0),t(e).currentGallery.navigation.next?(s(),l("div",ee,[r[4]||(r[4]=a("h3",null,"下一套",-1)),n(G,{class:"nav-card",hoverable:"",onClick:r[1]||(r[1]=c=>x(t(e).currentGallery.navigation.next.xrid))},{default:d(()=>[a("div",te,[n(p,{src:t(e).currentGallery.navigation.next.cover,alt:t(e).currentGallery.navigation.next.title,"object-fit":"cover","preview-disabled":"",class:"nav-image"},null,8,["src","alt"]),a("div",ae,[a("h4",null,i(t(e).currentGallery.navigation.next.title),1),n(_,{size:"small"},{default:d(()=>[v("ID: "+i(t(e).currentGallery.navigation.next.xrid),1)]),_:1})])])]),_:1})])):u("",!0)])):u("",!0)])):u("",!0)])])])}}},ie=E(re,[["__scopeId","data-v-6c0627d4"]]);export{ie as default};
