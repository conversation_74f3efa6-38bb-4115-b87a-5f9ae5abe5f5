import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGalleryStore } from '@/stores/gallery'
import { useMessage } from 'naive-ui'

/**
 * 图库相关的组合式函数
 * 提供图库数据管理、分页、排序等功能
 */
export function useGallery() {
  const galleryStore = useGalleryStore()
  const message = useMessage()
  
  // 本地状态
  const isInitialized = ref(false)
  const retryCount = ref(0)
  const maxRetries = 3
  
  // 计算属性
  const hasData = computed(() => galleryStore.hasGalleries)
  const isLoading = computed(() => galleryStore.loading)
  const error = computed(() => galleryStore.error)
  const galleries = computed(() => galleryStore.galleries)
  const pagination = computed(() => galleryStore.pagination)
  const sortOrder = computed(() => galleryStore.sortOrder)
  
  // 加载图库列表
  async function loadGalleries(showMessage = false) {
    try {
      await galleryStore.fetchGalleries()
      retryCount.value = 0
      
      if (showMessage && hasData.value) {
        message.success(`加载了 ${galleries.value.length} 个图库`)
      }
      
      return true
    } catch (error) {
      console.error('加载图库失败:', error)
      
      if (retryCount.value < maxRetries) {
        retryCount.value++
        message.warning(`加载失败，正在重试 (${retryCount.value}/${maxRetries})`)
        
        // 延迟重试
        setTimeout(() => {
          loadGalleries(showMessage)
        }, 1000 * retryCount.value)
      } else {
        message.error('加载图库失败，请检查网络连接')
      }
      
      return false
    }
  }
  
  // 切换排序
  async function toggleSort() {
    try {
      await galleryStore.toggleSort()
      message.info(`已切换为${sortOrder.value === 'latest' ? '最新' : '最旧'}排序`)
    } catch (error) {
      message.error('切换排序失败')
    }
  }
  
  // 跳转到指定页面
  async function goToPage(page) {
    if (page < 1 || page > pagination.value.totalPages) {
      message.warning(`页码超出范围 (1-${pagination.value.totalPages})`)
      return
    }

    try {
      await galleryStore.goToPage(page)
    } catch (error) {
      message.error('跳转页面失败')
    }
  }
  
  // 下一页
  async function nextPage() {
    if (!galleryStore.hasMore) {
      message.info('已经是最后一页了')
      return
    }
    
    try {
      await galleryStore.nextPage()
    } catch (error) {
      message.error('加载下一页失败')
    }
  }
  
  // 上一页
  async function prevPage() {
    if (pagination.value.currentPage <= 1) {
      message.info('已经是第一页了')
      return
    }
    
    try {
      await galleryStore.prevPage()
    } catch (error) {
      message.error('加载上一页失败')
    }
  }
  
  // 更改每页数量
  async function changePageSize(pageSize) {
    const validSizes = [15, 30, 50]
    if (!validSizes.includes(pageSize)) {
      message.warning('无效的页面大小')
      return
    }

    try {
      await galleryStore.changePageSize(pageSize)
      message.success(`已切换为每页 ${pageSize} 个`)
    } catch (error) {
      message.error('更改页面大小失败')
    }
  }
  
  // 刷新数据
  async function refresh() {
    galleryStore.reset()
    await loadGalleries(true)
  }
  
  // 初始化
  async function initialize() {
    if (isInitialized.value) return
    
    try {
      await loadGalleries()
      isInitialized.value = true
    } catch (error) {
      console.error('初始化图库失败:', error)
    }
  }
  
  // 重置状态
  function reset() {
    galleryStore.reset()
    isInitialized.value = false
    retryCount.value = 0
  }
  
  return {
    // 状态
    hasData,
    isLoading,
    error,
    galleries,
    pagination,
    sortOrder,
    isInitialized,
    retryCount,
    maxRetries,
    
    // 方法
    loadGalleries,
    toggleSort,
    goToPage,
    nextPage,
    prevPage,
    changePageSize,
    refresh,
    initialize,
    reset
  }
}

/**
 * 图片懒加载相关的组合式函数
 */
export function useImageLazyLoading() {
  const loadedImages = ref(new Set())
  const loadingImages = ref(new Set())
  const errorImages = ref(new Set())
  const observer = ref(null)
  
  // 创建Intersection Observer
  function createObserver() {
    if (typeof window === 'undefined' || !window.IntersectionObserver) {
      return null
    }
    
    return new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target
            const src = img.dataset.src
            
            if (src && !loadedImages.value.has(src)) {
              loadImage(img, src)
              observer.value?.unobserve(img)
            }
          }
        })
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    )
  }
  
  // 加载图片
  function loadImage(img, src) {
    if (loadingImages.value.has(src)) return
    
    loadingImages.value.add(src)
    
    const image = new Image()
    
    image.onload = () => {
      img.src = src
      img.classList.add('loaded')
      loadedImages.value.add(src)
      loadingImages.value.delete(src)
    }
    
    image.onerror = () => {
      img.classList.add('error')
      errorImages.value.add(src)
      loadingImages.value.delete(src)
    }
    
    image.src = src
  }
  
  // 观察图片元素
  function observeImage(img) {
    if (observer.value && img) {
      observer.value.observe(img)
    }
  }
  
  // 取消观察图片元素
  function unobserveImage(img) {
    if (observer.value && img) {
      observer.value.unobserve(img)
    }
  }
  
  // 初始化
  function initialize() {
    observer.value = createObserver()
  }
  
  // 清理
  function cleanup() {
    if (observer.value) {
      observer.value.disconnect()
      observer.value = null
    }
    
    loadedImages.value.clear()
    loadingImages.value.clear()
    errorImages.value.clear()
  }
  
  // 重置状态
  function reset() {
    loadedImages.value.clear()
    loadingImages.value.clear()
    errorImages.value.clear()
  }
  
  onMounted(() => {
    initialize()
  })
  
  onUnmounted(() => {
    cleanup()
  })
  
  return {
    loadedImages,
    loadingImages,
    errorImages,
    observeImage,
    unobserveImage,
    reset,
    cleanup
  }
}
