use anyhow::Result;
use mysql_async::{Pool, Row, Value};
use mysql_async::prelude::*;
use tracing::info;
use crate::models::ListItem;



// Helper trait to convert a row to a specific struct
trait FromRow: Sized {
    fn from_row(row: Row) -> Self;
}

/// 🗄️ 数据库管理系统 (使用 mysql_async)
#[derive(Clone)]
pub struct Database {
    pool: Pool,
}

impl Database {
    /// 🚀 数据库连接初始化
    pub async fn new(database_url: &str) -> Result<Self> {
        info!("🔗 初始化MySQL数据库连接 (使用 mysql_async)...");

        let opts = mysql_async::Opts::from_url(database_url)?;
        let pool = Pool::new(opts);
        let mut conn = pool.get_conn().await?;

        // 🧪 测试连接
        let row: Option<(i64,)> = conn.query_first("SELECT 1").await?;
        if row.is_none() || row.unwrap().0 != 1 {
            return Err(anyhow::anyhow!("数据库连接测试失败"));
        }

        info!("✅ 数据库连接成功");

        let db = Database { pool };
        db.ensure_tables().await?;
        Ok(db)
    }

    /// 🏗️ 表结构管理
    async fn ensure_tables(&self) -> Result<()> {
        info!("🔧 检查并创建表结构...");
        let mut conn = self.pool.get_conn().await?;

        conn.query_drop(
            r#"
            CREATE TABLE IF NOT EXISTS xr (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                xrid INT UNIQUE NOT NULL,
                issave TINYINT DEFAULT 0,
                fm VARCHAR(255),
                refm VARCHAR(255),
                title VARCHAR(255),
                url VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_xrid (xrid),
                INDEX idx_issave (issave)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "#,
        ).await?;

        conn.query_drop(
            r#"
            CREATE TABLE IF NOT EXISTS xrinfo (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                xrid INT NOT NULL,
                ourl VARCHAR(100) UNIQUE NOT NULL,
                reurl VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_xrid (xrid),
                INDEX idx_ourl (ourl),
                INDEX idx_reurl (reurl)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "#,
        ).await?;

        info!("✅ 表结构检查完成");
        Ok(())
    }

    /// 🚀 高性能批量插入或更新
    pub async fn batch_create_xr(&self, items: &[ListItem]) -> Result<usize> {
        if items.is_empty() {
            return Ok(0);
        }
        info!("💾 开始批量处理 {} 条记录", items.len());
        let mut conn = self.pool.get_conn().await?;
        let mut tx = conn.start_transaction(mysql_async::TxOpts::default()).await?;
        let mut processed_count = 0;

        // 使用 INSERT ... ON DUPLICATE KEY UPDATE 语句
        let stmt = tx.prep(
            r#"
            INSERT INTO xr (xrid, fm, title, url, issave)
            VALUES (?, ?, ?, ?, 0)
            ON DUPLICATE KEY UPDATE
                title = VALUES(title),
                fm = VALUES(fm),
                url = VALUES(url),
                updated_at = CURRENT_TIMESTAMP
            "#
        ).await?;

        for item in items {
            let result = tx.exec_iter(&stmt, (&item.xrid, &item.fm, &item.title, &item.url)).await?;
            processed_count += result.affected_rows() as usize;

            // 记录是插入还是更新
            if result.affected_rows() == 1 {
                info!("📝 新增记录: xrid={}, title={}", item.xrid, item.title);
            } else if result.affected_rows() == 2 {
                info!("🔄 更新记录: xrid={}, title={}", item.xrid, item.title);
            }
        }

        tx.commit().await?;
        info!("✅ 批量处理完成: 处理 {} 条记录", processed_count);
        Ok(processed_count)
    }

    /// 📊 获取系统统计信息
    pub async fn get_stats(&self) -> Result<DatabaseStats> {
        let mut conn = self.pool.get_conn().await?;
        let total_records: i64 = conn.query_first("SELECT COUNT(*) FROM xr").await?.unwrap_or((0,)).0;
        let processed_records: i64 = conn.query_first("SELECT COUNT(*) FROM xr WHERE issave = 1").await?.unwrap_or((0,)).0;
        let pending_records: i64 = conn.query_first("SELECT COUNT(*) FROM xr WHERE issave = 0 AND xrid > 16000").await?.unwrap_or((0,)).0;
        let processing_records: i64 = conn.query_first("SELECT COUNT(*) FROM xr WHERE issave = 3").await?.unwrap_or((0,)).0;
        let total_images: i64 = conn.query_first("SELECT COUNT(*) FROM xrinfo").await?.unwrap_or((0,)).0;
        let processed_images: i64 = conn.query_first("SELECT COUNT(*) FROM xrinfo WHERE reurl IS NOT NULL AND reurl != '' AND reurl != '4040'").await?.unwrap_or((0,)).0;

        Ok(DatabaseStats {
            total_records,
            processed_records,
            pending_records,
            processing_records,
            total_images,
            processed_images,
        })
    }

    /// 🔍 查找最新的记录ID
    pub async fn get_max_xrid(&self) -> Result<Option<i32>> {
        let mut conn = self.pool.get_conn().await?;
        let result: Option<(i32,)> = conn.query_first("SELECT MAX(xrid) FROM xr").await?;
        Ok(result.map(|(max_id,)| max_id))
    }

    /// 🔍 获取待处理的内页记录
    pub async fn get_pending_detail_record(&self) -> Result<Option<PendingDetailRecord>> {
        let mut conn = self.pool.get_conn().await?;
        let row: Option<Row> = conn.query_first(
            r#"
            SELECT id, xrid, url, title
            FROM xr
            WHERE issave IN (0, 2) and xrid > 16000
            ORDER BY id DESC
            LIMIT 1
            "#
        ).await?;
        Ok(row.map(PendingDetailRecord::from_row))
    }

    /// 🔄 更新记录状态
    pub async fn update_record_status(&self, id: i64, status: i32) -> Result<()> {
        let mut conn = self.pool.get_conn().await?;
        conn.exec_drop(
            "UPDATE xr SET issave = :status WHERE id = :id",
            params! { "status" => status, "id" => id },
        ).await?;
        Ok(())
    }

    /// 🖼️ 批量保存图片记录
    pub async fn batch_create_images(&self, xrid: i32, image_urls: &[String]) -> Result<usize> {
        if image_urls.is_empty() {
            return Ok(0);
        }
        info!("💾 开始批量插入 {} 张图片记录", image_urls.len());
        let mut conn = self.pool.get_conn().await?;
        let mut tx = conn.start_transaction(mysql_async::TxOpts::default()).await?;
        let mut new_count = 0;
        let stmt = tx.prep("INSERT IGNORE INTO xrinfo (xrid, ourl) VALUES (?, ?)").await?;

        for url in image_urls {
            let result = tx.exec_iter(&stmt, (xrid, url)).await?;
            new_count += result.affected_rows() as usize;
        }
        tx.commit().await?;
        info!("💾 图片记录保存成功: 新增{}张", new_count);
        Ok(new_count)
    }

    /// 📊 获取图片统计信息
    pub async fn get_image_stats(&self, xrid: i32) -> Result<ImageStats> {
        let mut conn = self.pool.get_conn().await?;
        let row: Option<Row> = conn.exec_first(
            r#"
            SELECT
                COUNT(*) as total_count,
                COUNT(reurl) as processed_count
            FROM xrinfo
            WHERE xrid = :xrid
            "#,
            params! { "xrid" => xrid },
        ).await?;
        Ok(row.map(ImageStats::from_row).unwrap_or(ImageStats { total_count: 0, processed_count: 0 }))
    }

    /// 🔍 检查已有图片记录
    pub async fn check_existing_images(&self, xrid: i32) -> Result<(i64, i64)> {
        let mut conn = self.pool.get_conn().await?;
        let total_count: i64 = conn.exec_first("SELECT COUNT(*) FROM xrinfo WHERE xrid = :xrid", params!{"xrid" => xrid}).await?.unwrap_or((0,)).0;
        let success_count: i64 = conn.exec_first("SELECT COUNT(*) FROM xrinfo WHERE xrid = :xrid AND reurl LIKE '/file/%'", params!{"xrid" => xrid}).await?.unwrap_or((0,)).0;
        Ok((total_count, success_count))
    }

    /// 🗑️ 清理已有图片记录
    pub async fn clean_existing_images(&self, xrid: i32) -> Result<u64> {
        let mut conn = self.pool.get_conn().await?;
        let result = conn.exec_iter(
            "DELETE FROM xrinfo WHERE xrid = :xrid",
            params! { "xrid" => xrid },
        ).await?;
        Ok(result.affected_rows())
    }

    /// 🖼️ 获取待处理的图片记录
    pub async fn get_pending_image_record(&self) -> Result<Option<PendingImageRecord>> {
        let mut conn = self.pool.get_conn().await?;
        let row: Option<Row> = conn.query_first(
            r#"
            SELECT id, xrid, ourl
            FROM xrinfo
            WHERE (reurl IS NULL OR reurl = '' OR reurl = '4040' OR reurl = 'processing') AND xrid > 16000
            ORDER BY reurl ASC, xrid DESC
            LIMIT 1
            "#
        ).await?;
        Ok(row.map(PendingImageRecord::from_row))
    }

    /// 🔍 检查是否已有相同ourl的成功上传记录
    pub async fn check_existing_reurl(&self, ourl: &str) -> Result<Option<String>> {
        let mut conn = self.pool.get_conn().await?;
        let result: Option<(String,)> = conn.exec_first(
            r#"
            SELECT reurl
            FROM xrinfo
            WHERE ourl = :ourl AND reurl LIKE '/file/%'
            ORDER BY id DESC
            LIMIT 1
            "#,
            params! { "ourl" => ourl },
        ).await?;
        Ok(result.map(|(reurl,)| reurl))
    }

    /// 🔄 更新图片记录的reurl字段
    pub async fn update_image_reurl(&self, id: i64, reurl: &str) -> Result<()> {
        let mut conn = self.pool.get_conn().await?;
        conn.exec_drop(
            "UPDATE xrinfo SET reurl = :reurl WHERE id = :id",
            params! { "reurl" => reurl, "id" => id },
        ).await?;
        Ok(())
    }

    /// 🎨 获取待处理的封面记录
    pub async fn get_pending_cover_record(&self) -> Result<Option<PendingCoverRecord>> {
        let mut conn = self.pool.get_conn().await?;
        let row: Option<Row> = conn.query_first(
            r#"
            SELECT id, xrid, fm
            FROM xr
            WHERE (refm IS NULL OR refm = '4040' OR refm = 'processing') AND xrid > 16000
            ORDER BY refm ASC, xrid DESC
            LIMIT 1
            "#
        ).await?;
        Ok(row.map(PendingCoverRecord::from_row))
    }

    /// 🔍 检查是否已有相同fm的成功上传记录
    pub async fn check_existing_refm(&self, fm: &str) -> Result<Option<String>> {
        let mut conn = self.pool.get_conn().await?;
        let result: Option<(String,)> = conn.exec_first(
            r#"
            SELECT refm
            FROM xr
            WHERE fm = :fm AND refm LIKE '/file/%'
            ORDER BY id DESC
            LIMIT 1
            "#,
            params! { "fm" => fm },
        ).await?;
        Ok(result.map(|(refm,)| refm))
    }

    /// 🔄 更新封面记录的refm字段
    pub async fn update_cover_refm(&self, id: i64, refm: &str) -> Result<()> {
        let mut conn = self.pool.get_conn().await?;
        conn.exec_drop(
            "UPDATE xr SET refm = :refm WHERE id = :id",
            params! { "refm" => refm, "id" => id },
        ).await?;
        Ok(())
    }
    
    /// 🔄 重置处理中状态的记录
    pub async fn reset_processing_records(&self) -> Result<u64> {
        let mut conn = self.pool.get_conn().await?;
        let result = conn.query_iter("UPDATE xr SET issave = 0 WHERE issave = 3").await?;
        Ok(result.affected_rows())
    }

    /// 📊 获取图片上传统计信息
    pub async fn get_reurl_stats(&self) -> Result<serde_json::Value> {
        let mut conn = self.pool.get_conn().await?;
        let total: i64 = conn.query_first("SELECT COUNT(*) FROM xrinfo").await?.unwrap_or((0,)).0;
        let success: i64 = conn.query_first("SELECT COUNT(*) FROM xrinfo WHERE reurl LIKE '/file/%'").await?.unwrap_or((0,)).0;
        let pending: i64 = conn.query_first("SELECT COUNT(*) FROM xrinfo WHERE reurl IS NULL").await?.unwrap_or((0,)).0;
        let failed: i64 = conn.query_first("SELECT COUNT(*) FROM xrinfo WHERE reurl IS NOT NULL AND reurl NOT LIKE '/file/%'").await?.unwrap_or((0,)).0;

        let rows: Vec<Row> = conn.query("SELECT reurl, COUNT(*) as count FROM xrinfo WHERE reurl IS NOT NULL AND reurl NOT LIKE '/file/%' GROUP BY reurl ORDER BY count DESC").await?;
        let mut error_breakdown = serde_json::Map::new();
        for mut row in rows {
            let reurl: String = row.take("reurl").unwrap_or_default();
            let count: i64 = row.take("count").unwrap_or_default();
            error_breakdown.insert(reurl, serde_json::Value::Number(count.into()));
        }

        Ok(serde_json::json!({
            "total": total,
            "success": success,
            "pending": pending,
            "failed": failed,
            "success_rate": if total > 0 { (success as f64 / total as f64 * 100.0).round() } else { 0.0 },
            "error_breakdown": error_breakdown
        }))
    }

    /// 📊 获取图片爬取统计信息
    pub async fn get_imglist_stats(&self) -> Result<serde_json::Value> {
        let mut conn = self.pool.get_conn().await?;
        let total_records: i64 = conn.query_first("SELECT COUNT(*) FROM xr WHERE xrid > 16000").await?.unwrap_or((0,)).0;
        let completed: i64 = conn.query_first("SELECT COUNT(*) FROM xr WHERE issave = 1 AND xrid > 16000").await?.unwrap_or((0,)).0;
        let pending: i64 = conn.query_first("SELECT COUNT(*) FROM xr WHERE issave = 0 AND xrid > 16000").await?.unwrap_or((0,)).0;
        let processing: i64 = conn.query_first("SELECT COUNT(*) FROM xr WHERE issave = 3 AND xrid > 16000").await?.unwrap_or((0,)).0;
        let total_images: i64 = conn.query_first("SELECT COUNT(*) FROM xrinfo").await?.unwrap_or((0,)).0;

        Ok(serde_json::json!({
            "total_records": total_records,
            "completed": completed,
            "pending": pending,
            "processing": processing,
            "completion_rate": if total_records > 0 { (completed as f64 / total_records as f64 * 100.0).round() } else { 0.0 },
            "total_images": total_images
        }))
    }

    /// 🧹 清理无效数据
    pub async fn cleanup_invalid_data(&self) -> Result<(u64, u64)> {
        let mut conn = self.pool.get_conn().await?;
        let cleared_reurl = conn.query_iter(
            r#"
            UPDATE xrinfo
            SET reurl = NULL
            WHERE reurl IS NOT NULL
            AND reurl NOT LIKE '/file/%'
            AND xrid >= 16000
            "#
        ).await?.affected_rows();

        let reset_records = conn.query_iter(
            r#"
            UPDATE xr
            SET issave = 0
            WHERE xrid IN (
                SELECT DISTINCT xrid
                FROM xrinfo
                WHERE reurl IS NULL AND xrid >= 16000
            )
            "#
        ).await?.affected_rows();

        Ok((cleared_reurl, reset_records))
    }

    /// 🔄 重置issave状态
    pub async fn reset_issave_status(&self) -> Result<u64> {
        let mut conn = self.pool.get_conn().await?;
        let result = conn.query_iter("UPDATE xr SET issave = 0 WHERE issave != 1 AND xrid > 16000").await?;
        Ok(result.affected_rows())
    }
    
    /// 🔄 获取失败的图片记录用于重试
    pub async fn get_failed_image_records(&self, error_codes: &[String], limit: i64) -> Result<Vec<PendingImageRecord>> {
        let mut conn = self.pool.get_conn().await?;
        let placeholders = error_codes.iter().map(|_| "?").collect::<Vec<_>>().join(",");
        let query = format!(
            r#"
            SELECT id, xrid, ourl
            FROM xrinfo
            WHERE reurl IN ({})
            ORDER BY xrid DESC
            LIMIT ?
            "#,
            placeholders
        );

        let mut params: Vec<Value> = error_codes.iter().map(|s| Value::from(s)).collect();
        params.push(Value::from(limit));

        let rows: Vec<Row> = conn.exec(&query, params).await?;
        Ok(rows.into_iter().map(PendingImageRecord::from_row).collect())
    }
}

/// 📊 数据库统计信息
#[derive(Debug, serde::Serialize)]
pub struct DatabaseStats {
    pub total_records: i64,
    pub processed_records: i64,
    pub pending_records: i64,
    pub processing_records: i64,
    pub total_images: i64,
    pub processed_images: i64,
}

/// 🔍 待处理的详情记录
#[derive(Debug, serde::Serialize)]
pub struct PendingDetailRecord {
    pub id: i64,
    pub xrid: i64,
    pub url: Option<String>,
    pub title: Option<String>,
}

impl FromRow for PendingDetailRecord {
    fn from_row(mut row: Row) -> Self {
        Self {
            id: row.take("id").unwrap(),
            xrid: row.take("xrid").unwrap(),
            url: row.take("url").unwrap(),
            title: row.take("title").unwrap(),
        }
    }
}

/// 📊 图片统计信息
#[derive(Debug, serde::Serialize)]
pub struct ImageStats {
    pub total_count: i64,
    pub processed_count: i64,
}

impl FromRow for ImageStats {
    fn from_row(mut row: Row) -> Self {
        Self {
            total_count: row.take("total_count").unwrap(),
            processed_count: row.take("processed_count").unwrap(),
        }
    }
}

/// 🖼️ 待处理的图片记录 (reurl)
#[derive(Debug, serde::Serialize)]
pub struct PendingImageRecord {
    pub id: i64,
    pub xrid: i64,
    pub ourl: Option<String>,
}

impl FromRow for PendingImageRecord {
    fn from_row(mut row: Row) -> Self {
        Self {
            id: row.take("id").unwrap(),
            xrid: row.take("xrid").unwrap(),
            ourl: row.take("ourl").unwrap(),
        }
    }
}

/// 🎨 待处理的封面记录 (refmurl)
#[derive(Debug, serde::Serialize)]
pub struct PendingCoverRecord {
    pub id: i64,
    pub xrid: i64,
    pub fm: Option<String>,
}

impl FromRow for PendingCoverRecord {
    fn from_row(mut row: Row) -> Self {
        Self {
            id: row.take("id").unwrap(),
            xrid: row.take("xrid").unwrap(),
            fm: row.take("fm").unwrap(),
        }
    }
}
