{"rustc": 3926191382657067107, "features": "[\"default\"]", "declared_features": "[\"default\", \"has_bytes\", \"log\", \"nats\", \"nats_storage\", \"postgres-native-tls\", \"postgres-openssl\", \"postgres_native_tls\", \"postgres_openssl\", \"postgres_storage\", \"prost\", \"prost-build\", \"signal\", \"tokio-postgres\", \"tracing-subscriber\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 14025003461467619869, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-cron-scheduler-ee40580d48770463\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}