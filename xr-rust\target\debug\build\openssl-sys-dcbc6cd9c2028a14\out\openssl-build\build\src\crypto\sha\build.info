LIBS=../../libcrypto

$SHA1ASM=
IF[{- !$disabled{asm} -}]
  $SHA1ASM_x86=sha1-586.S sha256-586.S sha512-586.S
  $SHA1DEF_x86=SHA1_ASM SHA256_ASM SHA512_ASM
  $SHA1ASM_x86_64=\
        sha1-x86_64.s sha256-x86_64.s sha512-x86_64.s sha1-mb-x86_64.s \
        sha256-mb-x86_64.s
  $SHA1DEF_x86_64=SHA1_ASM SHA256_ASM SHA512_ASM

  $SHA1ASM_ia64=sha1-ia64.s sha256-ia64.s sha512-ia64.s
  $SHA1DEF_ia64=SHA1_ASM SHA256_ASM SHA512_ASM

  $SHA1ASM_sparcv9=sha1-sparcv9.S sha256-sparcv9.S sha512-sparcv9.S
  $SHA1DEF_sparcv9=SHA1_ASM SHA256_ASM SHA512_ASM

  $SHA1ASM_alpha=sha1-alpha.S
  $SHA1DEF_alpha=SHA1_ASM

  $SHA1ASM_mips32=sha1-mips.S sha256-mips.S
  $SHA1DEF_mips32=SHA1_ASM SHA256_ASM
  $SHA1ASM_mips64=$SHA1ASM_mips32 sha512-mips.S
  $SHA1DEF_mips64=$SHA1DEF_mips32 SHA512_ASM

  $SHA1ASM_s390x=sha1-s390x.S sha256-s390x.S sha512-s390x.S
  $SHA1DEF_s390x=SHA1_ASM SHA256_ASM SHA512_ASM

  $SHA1ASM_armv4=sha1-armv4-large.S sha256-armv4.S sha512-armv4.S
  $SHA1DEF_armv4=SHA1_ASM SHA256_ASM SHA512_ASM
  $SHA1ASM_aarch64=sha1-armv8.S sha256-armv8.S sha512-armv8.S
  $SHA1DEF_aarch64=SHA1_ASM SHA256_ASM SHA512_ASM

  $SHA1ASM_parisc11=sha1-parisc.s sha256-parisc.s sha512-parisc.s
  $SHA1DEF_parisc11=SHA1_ASM SHA256_ASM SHA512_ASM
  $SHA1ASM_parisc20_64=$SHA1ASM_parisc11
  $SHA1DEF_parisc20_64=$SHA1DEF_parisc11

  $SHA1ASM_ppc32=\
        sha_ppc.c sha1-ppc.s sha256-ppc.s sha512-ppc.s sha256p8-ppc.s \
        sha512p8-ppc.s
  $SHA1DEF_ppc32=SHA1_ASM SHA256_ASM SHA512_ASM
  $SHA1ASM_ppc64=$SHA1ASM_ppc32
  $SHA1DEF_ppc64=$SHA1DEF_ppc32

  $SHA1ASM_c64xplus=sha1-c64xplus.s sha256-c64xplus.s sha512-c64xplus.s
  $SHA1DEF_c64xplus=SHA1_ASM SHA256_ASM SHA512_ASM

  $SHA1ASM_riscv64=sha_riscv.c sha256-riscv64-zvkb-zvknha_or_zvknhb.S sha512-riscv64-zvkb-zvknhb.S
  $SHA1DEF_riscv64=SHA256_ASM INCLUDE_C_SHA256 SHA512_ASM INCLUDE_C_SHA512

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$SHA1ASM_{- $target{asm_arch} -}]
    $SHA1ASM=$SHA1ASM_{- $target{asm_arch} -}
    $SHA1DEF=$SHA1DEF_{- $target{asm_arch} -}
  ENDIF
ENDIF

$KECCAK1600ASM=keccak1600.c
IF[{- !$disabled{asm} -}]
  $KECCAK1600ASM_x86=
  $KECCAK1600ASM_x86_64=keccak1600-x86_64.s

  $KECCAK1600ASM_s390x=keccak1600-s390x.S

  $KECCAK1600ASM_armv4=keccak1600-armv4.S
  $KECCAK1600ASM_aarch64=keccak1600-armv8.S

  $KECCAK1600ASM_ppc64=keccak1600-ppc64.s

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$KECCAK1600ASM_{- $target{asm_arch} -}]
    $KECCAK1600ASM=$KECCAK1600ASM_{- $target{asm_arch} -}
    $KECCAK1600DEF=KECCAK1600_ASM
  ENDIF
ENDIF

$COMMON=sha1dgst.c sha256.c sha512.c sha3.c $SHA1ASM $KECCAK1600ASM
SOURCE[../../libcrypto]=$COMMON sha1_one.c
SOURCE[../../providers/libfips.a]= $COMMON

# Implementations are now spread across several libraries, so the defines
# need to be applied to all affected libraries and modules.
DEFINE[../../libcrypto]=$SHA1DEF $KECCAK1600DEF
DEFINE[../../providers/libfips.a]=$SHA1DEF $KECCAK1600DEF
DEFINE[../../providers/libdefault.a]=$SHA1DEF $KECCAK1600DEF
# We only need to include the SHA1DEF and KECCAK1600DEF stuff in the
# legacy provider when it's a separate module and it's dynamically
# linked with libcrypto.  Otherwise, it already gets everything that
# the static libcrypto.a has, and doesn't need it added again.
IF[{- !$disabled{module} && !$disabled{shared} -}]
  DEFINE[../../providers/liblegacy.a]=$SHA1DEF $KECCAK1600DEF
ENDIF

GENERATE[sha1-586.S]=asm/sha1-586.pl
DEPEND[sha1-586.S]=../perlasm/x86asm.pl
GENERATE[sha256-586.S]=asm/sha256-586.pl
DEPEND[sha256-586.S]=../perlasm/x86asm.pl
GENERATE[sha512-586.S]=asm/sha512-586.pl
DEPEND[sha512-586.S]=../perlasm/x86asm.pl

GENERATE[sha1-ia64.s]=asm/sha1-ia64.pl
GENERATE[sha256-ia64.s]=asm/sha512-ia64.pl
GENERATE[sha512-ia64.s]=asm/sha512-ia64.pl

GENERATE[sha1-alpha.S]=asm/sha1-alpha.pl

GENERATE[sha1-x86_64.s]=asm/sha1-x86_64.pl
GENERATE[sha1-mb-x86_64.s]=asm/sha1-mb-x86_64.pl
GENERATE[sha256-x86_64.s]=asm/sha512-x86_64.pl
GENERATE[sha256-mb-x86_64.s]=asm/sha256-mb-x86_64.pl
GENERATE[sha512-x86_64.s]=asm/sha512-x86_64.pl
GENERATE[keccak1600-x86_64.s]=asm/keccak1600-x86_64.pl

GENERATE[sha1-sparcv9a.S]=asm/sha1-sparcv9a.pl
GENERATE[sha1-sparcv9.S]=asm/sha1-sparcv9.pl
INCLUDE[sha1-sparcv9.o]=..
GENERATE[sha256-sparcv9.S]=asm/sha512-sparcv9.pl
INCLUDE[sha256-sparcv9.o]=..
GENERATE[sha512-sparcv9.S]=asm/sha512-sparcv9.pl
INCLUDE[sha512-sparcv9.o]=..

GENERATE[sha1-ppc.s]=asm/sha1-ppc.pl
GENERATE[sha256-ppc.s]=asm/sha512-ppc.pl
GENERATE[sha512-ppc.s]=asm/sha512-ppc.pl
GENERATE[sha256p8-ppc.s]=asm/sha512p8-ppc.pl
GENERATE[sha512p8-ppc.s]=asm/sha512p8-ppc.pl
GENERATE[keccak1600-ppc64.s]=asm/keccak1600-ppc64.pl

GENERATE[sha1-parisc.s]=asm/sha1-parisc.pl
GENERATE[sha256-parisc.s]=asm/sha512-parisc.pl
GENERATE[sha512-parisc.s]=asm/sha512-parisc.pl

GENERATE[sha1-mips.S]=asm/sha1-mips.pl
INCLUDE[sha1-mips.o]=..
GENERATE[sha256-mips.S]=asm/sha512-mips.pl
INCLUDE[sha256-mips.o]=..
GENERATE[sha512-mips.S]=asm/sha512-mips.pl
INCLUDE[sha512-mips.o]=..

GENERATE[sha1-armv4-large.S]=asm/sha1-armv4-large.pl
INCLUDE[sha1-armv4-large.o]=..
GENERATE[sha256-armv4.S]=asm/sha256-armv4.pl
INCLUDE[sha256-armv4.o]=..
GENERATE[sha512-armv4.S]=asm/sha512-armv4.pl
INCLUDE[sha512-armv4.o]=..
GENERATE[keccak1600-armv4.S]=asm/keccak1600-armv4.pl
INCLUDE[keccak1600-armv4.o]=..

GENERATE[sha1-armv8.S]=asm/sha1-armv8.pl
INCLUDE[sha1-armv8.o]=..
GENERATE[sha256-armv8.S]=asm/sha512-armv8.pl
INCLUDE[sha256-armv8.o]=..
GENERATE[sha512-armv8.S]=asm/sha512-armv8.pl
INCLUDE[sha512-armv8.o]=..
GENERATE[keccak1600-armv8.S]=asm/keccak1600-armv8.pl
INCLUDE[keccak1600-armv8.o]=..

GENERATE[sha1-s390x.S]=asm/sha1-s390x.pl
INCLUDE[sha1-s390x.o]=..
GENERATE[sha256-s390x.S]=asm/sha512-s390x.pl
INCLUDE[sha256-s390x.o]=..
GENERATE[sha512-s390x.S]=asm/sha512-s390x.pl
INCLUDE[sha512-s390x.o]=..
GENERATE[keccak1600-s390x.S]=asm/keccak1600-s390x.pl

GENERATE[sha1-c64xplus.S]=asm/sha1-c64xplus.pl
GENERATE[sha256-c64xplus.S]=asm/sha256-c64xplus.pl
GENERATE[sha512-c64xplus.S]=asm/sha512-c64xplus.pl

GENERATE[sha256-riscv64-zvkb-zvknha_or_zvknhb.S]=asm/sha256-riscv64-zvkb-zvknha_or_zvknhb.pl
GENERATE[sha512-riscv64-zvkb-zvknhb.S]=asm/sha512-riscv64-zvkb-zvknhb.pl

# These are not yet used and do not support multi-squeeze
GENERATE[keccak1600-c64x.S]=asm/keccak1600-c64x.pl
GENERATE[keccak1600-avx2.S]=asm/keccak1600-avx2.pl
GENERATE[keccak1600-avx512.S]=asm/keccak1600-avx512.pl
GENERATE[keccak1600-avx512vl.S]=asm/keccak1600-avx512vl.pl
GENERATE[keccak1600-mmx.S]=asm/keccak1600-mmx.pl
GENERATE[keccak1600p8-ppc.S]=asm/keccak1600p8-ppc.pl

GENERATE[sha1-thumb.S]=asm/sha1-thumb.pl
