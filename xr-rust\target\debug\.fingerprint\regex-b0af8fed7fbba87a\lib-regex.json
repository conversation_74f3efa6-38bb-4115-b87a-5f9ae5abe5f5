{"rustc": 3926191382657067107, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 11876527447619405325, "path": 15227960611824379394, "deps": [[555019317135488525, "regex_automata", false, 10644020694956234184], [2779309023524819297, "aho_corasick", false, 14651975387861492133], [9408802513701742484, "regex_syntax", false, 13793711508656381663], [15932120279885307830, "memchr", false, 9802535636153823867]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-b0af8fed7fbba87a\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}