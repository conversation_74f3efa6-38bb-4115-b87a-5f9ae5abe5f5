I:\xr\xr-rust\target\debug\deps\bigdecimal-904aee63c281d6e4.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\macros.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\addition.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\sqrt.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\cbrt.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\inverse.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_convert.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_trait_from_str.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_add.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_sub.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_mul.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_div.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_rem.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_cmp.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_num.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_fmt.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\parsing.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\rounding.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\context.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\./with_std.rs I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/default_precision.rs I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/exponential_format_threshold.rs I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/default_rounding_mode.rs

I:\xr\xr-rust\target\debug\deps\libbigdecimal-904aee63c281d6e4.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\macros.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\addition.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\sqrt.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\cbrt.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\inverse.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_convert.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_trait_from_str.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_add.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_sub.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_mul.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_div.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_rem.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_cmp.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_num.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_fmt.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\parsing.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\rounding.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\context.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\./with_std.rs I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/default_precision.rs I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/exponential_format_threshold.rs I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/default_rounding_mode.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\macros.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\addition.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\sqrt.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\cbrt.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\arithmetic\inverse.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_convert.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_trait_from_str.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_add.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_sub.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_mul.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_div.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_ops_rem.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_cmp.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_num.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\impl_fmt.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\parsing.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\rounding.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\context.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bigdecimal-0.4.8\src\./with_std.rs:
I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/default_precision.rs:
I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/exponential_format_threshold.rs:
I:\xr\xr-rust\target\debug\build\bigdecimal-18187a54f2eb65a1\out/default_rounding_mode.rs:

# env-dep:OUT_DIR=I:\\xr\\xr-rust\\target\\debug\\build\\bigdecimal-18187a54f2eb65a1\\out
