use anyhow::Result;
use axum::{
    routing::get,
    Router,
};
use clap::Parser;
use tokio::signal;
use tower::ServiceBuilder;
use tower_http::{cors::CorsLayer, trace::TraceLayer};
use tracing::{info, warn};

mod config;
mod database;
mod models;
mod services;
mod handlers;
mod error;

use config::Config;
use database::Database;
use services::{CrawlerService, CronService};

/// Command line arguments
#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// Port to run the server on
    #[arg(short, long)]
    port: Option<u16>,
}

/// 🚀 XR爬虫系统 - Rust科学级实现
///
/// 这是一个展示Rust语言强大能力的爬虫系统实现
/// 具备以下特性：
/// - 🔥 零成本抽象，极致性能
/// - 🛡️ 内存安全，编译时保证
/// - ⚡ 异步并发，高效处理
/// - 🎯 类型安全，运行时无忧
#[tokio::main]
async fn main() -> Result<()> {
    // 🎨 初始化结构化日志系统
    tracing_subscriber::fmt()
        .with_env_filter("xr_crawler=debug,tower_http=debug")
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .init();

    info!("🚀 启动XR爬虫系统 (Rust科学级实现)");
    info!("🔬 展示零成本抽象和内存安全的强大威力");

    // 命令行参数解析
    let args = Args::parse();

    // 📋 加载配置 - 类型安全的配置管理
    let config = Config::load(args.port).await?;
    info!("✅ 配置加载成功: 端口={}", config.server.port);

    // 🗄️ 初始化数据库 - 编译时SQL检查
    info!("🔗 初始化数据库连接...");
    let database = match Database::new(&config.database.url).await {
        Ok(db) => {
            info!("✅ 数据库连接成功");
            Some(db)
        }
        Err(e) => {
            warn!("⚠️  数据库连接失败，使用简化模式: {}", e);
            None
        }
    };

    // 🕷️ 创建爬虫服务 - 高性能异步爬虫
    let crawler_service = CrawlerService::new(&config);

    // 🕐 创建定时任务服务 - Rust科学级定时任务
    let cron_service = match CronService::new(config.server.port).await {
        Ok(service) => {
            info!("✅ 定时任务服务创建成功");
            Some(service)
        }
        Err(e) => {
            warn!("⚠️  定时任务服务创建失败: {}", e);
            None
        }
    };

    // 🏗️ 创建应用状态 - 零拷贝状态管理
    let app_state = AppState {
        config: config.clone(),
        database,
        crawler_service,
        cron_service: cron_service.as_ref().cloned(),
    };

    // 🛣️ 构建路由 - 类型安全的路由系统
    let app = create_router(app_state);

    // 🕐 启动定时任务系统
    if let Some(ref cron) = cron_service {
        if let Err(e) = cron.start().await {
            warn!("⚠️  定时任务启动失败: {}", e);
        }
    }

    // 🌐 启动服务器
    let addr = format!("{}:{}", config.server.host, config.server.port);
    info!("🎉 服务器启动成功！");
    info!("📡 服务地址: http://127.0.0.1:{}", config.server.port);
    info!("🔥 准备展示Rust的极致性能！");

    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal(cron_service))
        .await?;

    Ok(())
}

/// 应用状态 - 零成本抽象的状态管理
#[derive(Clone)]
pub struct AppState {
    pub config: Config,
    pub database: Option<Database>,
    pub crawler_service: CrawlerService,
    pub cron_service: Option<CronService>,
}

/// 创建路由系统 - 类型安全的API设计
fn create_router(state: AppState) -> Router {
    Router::new()
        // 🏠 基础路由
        .route("/", get(handlers::home))

        // 📋 列表爬取路由
        .route("/getlist", get(handlers::get_list))

        // 🖼️ 图片爬取路由
        .route("/getimglist", get(handlers::get_img_list))
        .route("/getimglist/reset", get(handlers::getimglist_reset))
        .route("/getimglist/stats", get(handlers::getimglist_stats))

        // 📸 图片上传路由
        .route("/reurl", get(handlers::reurl))
        .route("/reurl/retry", get(handlers::reurl_retry))
        .route("/reurl/stats", get(handlers::reurl_stats))

        // 🎨 封面上传路由
        .route("/refmurl", get(handlers::refmurl))

        // 🧹 数据维护路由
        .route("/upnull", get(handlers::upnull))
        .route("/upsave", get(handlers::upsave))

        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive()),
        )
        .with_state(state)
}

/// 优雅关闭信号处理
async fn shutdown_signal(cron_service: Option<CronService>) {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    info!("🛑 收到关闭信号，正在优雅关闭...");

    // 🕐 停止定时任务
    if let Some(mut cron) = cron_service {
        if let Err(e) = cron.stop().await {
            warn!("⚠️  定时任务停止失败: {}", e);
        }
    }
}
