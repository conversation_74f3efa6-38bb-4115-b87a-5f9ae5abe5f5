[package]
name = "xr-crawler"
version = "1.0.0"
edition = "2021"
authors = ["XR Crawler Team"]
description = "🚀 XR爬虫系统 - Rust科学级实现"

[[bin]]
name = "xr-crawler"
path = "src/main.rs"

[dependencies]
# 🌐 Web框架 - 性能之王
axum = { version = "0.7", features = ["macros"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["cors", "trace", "fs"] }
hyper = { version = "1.0", features = ["full"] }
tokio = { version = "1.0", features = ["full", "macros", "rt-multi-thread"] }

# 🔄 序列化 - 零拷贝性能
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 🗄️ 数据库 - 异步MySQL驱动 (使用rustls避免OpenSSL依赖)
mysql_async = { version = "0.34.1", default-features = false, features = ["default-rustls"] }

# 🌍 HTTP客户端 - 异步性能怪兽 (纯Rust TLS)
reqwest = { version = "0.11", features = ["json", "stream", "gzip", "multipart", "rustls-tls"], default-features = false }

# 🕷️ HTML解析 - CSS选择器专家
scraper = "0.18"
regex = "1.10"

# 🔤 字符编码处理 - 多编码支持
encoding_rs = "0.8"

# ⚙️ 配置管理 - 环境友好
config = "0.14"
dotenvy = "0.15"

# 📊 日志系统 - 结构化追踪
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# ⏰ 时间处理 - 高精度时间
chrono = { version = "0.4", features = ["serde"] }
tokio-cron-scheduler = "0.10"

# 🔧 错误处理 - 人性化错误
anyhow = "1.0"
thiserror = "1.0"

# 🔀 异步工具 - 并发控制大师
futures = "0.3"
async-trait = "0.1"

# 🔐 加密工具 - 安全第一
uuid = { version = "1.0", features = ["v4", "serde"] }
sha2 = "0.10"
md5 = "0.7"

# 🛠️ 系统工具 - 高效实用
once_cell = "1.19"
bytes = "1.0"

# 命令行参数解析
clap = { version = "4.0", features = ["derive"] }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 1
