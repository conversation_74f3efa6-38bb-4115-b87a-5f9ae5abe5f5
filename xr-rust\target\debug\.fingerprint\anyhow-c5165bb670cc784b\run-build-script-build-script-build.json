{"rustc": 3926191382657067107, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 13952097610802125436]], "local": [{"RerunIfChanged": {"output": "debug\\build\\anyhow-c5165bb670cc784b\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}