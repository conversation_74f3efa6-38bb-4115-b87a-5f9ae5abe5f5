<template>
  <div class="detail-desktop">
    <!-- 桌面端头部 -->
    <header class="desktop-header">
      <div class="container">
        <n-button @click="goBack" type="primary" ghost>
          ← 返回首页
        </n-button>
        <div class="header-info" v-if="galleryStore.currentGallery">
          <h1 class="gallery-title">{{ galleryStore.currentGallery.info.title }}</h1>
          <div class="gallery-meta">
            <n-tag type="info">ID: {{ galleryStore.currentGallery.info.xrid }}</n-tag>
            <n-tag type="success">{{ galleryStore.currentGallery.images.length }} 张图片</n-tag>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="desktop-content">
      <div class="container">
        <!-- 加载状态 -->
        <Loading v-if="galleryStore.loading" text="加载详情中..." />

        <!-- 错误状态 -->
        <ErrorMessage
          v-else-if="galleryStore.error"
          :title="galleryStore.error"
          description="无法加载图片详情"
          show-back
          @retry="loadDetail"
        />

        <!-- 图片查看器 -->
        <div v-else-if="galleryStore.currentGallery" class="image-viewer">
          <!-- 主图区域 -->
          <div class="main-image-area">
            <div class="main-image-container">
              <img
                :src="currentImage.reurl"
                :alt="`图片 ${currentImage.order}`"
                class="main-image"
                @load="onMainImageLoad"
                @error="onMainImageError"
              />
              <div class="image-info">
                <span class="image-counter">{{ currentIndex + 1 }} / {{ galleryStore.currentGallery.images.length }}</span>
              </div>
            </div>
            
            <!-- 图片切换按钮 -->
            <button 
              v-if="currentIndex > 0"
              @click="previousImage"
              class="nav-button nav-button-prev"
            >
              ‹
            </button>
            <button 
              v-if="currentIndex < galleryStore.currentGallery.images.length - 1"
              @click="nextImage"
              class="nav-button nav-button-next"
            >
              ›
            </button>
          </div>

          <!-- 缩略图条 -->
          <div class="thumbnail-strip">
            <div class="thumbnail-container" ref="thumbnailContainer">
              <div
                v-for="(image, index) in galleryStore.currentGallery.images"
                :key="image.id"
                class="thumbnail-item"
                :class="{ 'active': index === currentIndex }"
                @click="selectImage(index)"
              >
                <img
                  :src="image.reurl"
                  :alt="`缩略图 ${image.order}`"
                  class="thumbnail-image"
                  loading="lazy"
                />
                <div class="thumbnail-number">{{ image.order }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 导航区域 -->
        <div class="desktop-navigation" v-if="hasNavigation">
          <div class="nav-item" v-if="galleryStore.currentGallery.navigation.prev">
            <h3>上一套</h3>
            <n-card
              class="nav-card"
              hoverable
              @click="goToGallery(galleryStore.currentGallery.navigation.prev.xrid)"
            >
              <div class="nav-content">
                <n-image
                  :src="galleryStore.currentGallery.navigation.prev.cover"
                  :alt="galleryStore.currentGallery.navigation.prev.title"
                  object-fit="cover"
                  preview-disabled
                  class="nav-image"
                />
                <div class="nav-info">
                  <h4>{{ galleryStore.currentGallery.navigation.prev.title }}</h4>
                  <n-tag size="small">ID: {{ galleryStore.currentGallery.navigation.prev.xrid }}</n-tag>
                </div>
              </div>
            </n-card>
          </div>

          <div class="nav-item" v-if="galleryStore.currentGallery.navigation.next">
            <h3>下一套</h3>
            <n-card
              class="nav-card"
              hoverable
              @click="goToGallery(galleryStore.currentGallery.navigation.next.xrid)"
            >
              <div class="nav-content">
                <n-image
                  :src="galleryStore.currentGallery.navigation.next.cover"
                  :alt="galleryStore.currentGallery.navigation.next.title"
                  object-fit="cover"
                  preview-disabled
                  class="nav-image"
                />
                <div class="nav-info">
                  <h4>{{ galleryStore.currentGallery.navigation.next.title }}</h4>
                  <n-tag size="small">ID: {{ galleryStore.currentGallery.navigation.next.xrid }}</n-tag>
                </div>
              </div>
            </n-card>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage, NButton, NTag, NCard, NImage } from 'naive-ui'
import { useGalleryStore } from '@/stores/gallery'
import { isMobileDevice } from '@/utils/device'
import Loading from '@/components/Loading.vue'
import ErrorMessage from '@/components/ErrorMessage.vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const galleryStore = useGalleryStore()

// 响应式数据
const currentIndex = ref(0)
const thumbnailContainer = ref(null)

// 计算属性
const hasNavigation = computed(() => {
  const nav = galleryStore.currentGallery?.navigation
  return nav && (nav.prev || nav.next)
})

const currentImage = computed(() => {
  return galleryStore.currentGallery?.images[currentIndex.value]
})

// 加载详情
async function loadDetail() {
  const xrid = parseInt(route.params.xrid)
  if (!xrid) {
    message.error('无效的图库ID')
    return
  }

  try {
    await galleryStore.fetchGalleryDetail(xrid)
    currentIndex.value = 0
  } catch (error) {
    message.error('加载详情失败')
  }
}

// 返回首页
function goBack() {
  try {
    router.push('/')
  } catch (error) {
    console.error('路由跳转失败:', error)
    message.error('返回首页失败')
  }
}

// 跳转到其他图库
function goToGallery(xrid) {
  router.push({ name: 'DetailDesktop', params: { xrid } })
}

// 选择图片
function selectImage(index) {
  currentIndex.value = index
  scrollToThumbnail(index)
}

// 上一张图片
function previousImage() {
  if (currentIndex.value > 0) {
    selectImage(currentIndex.value - 1)
  }
}

// 下一张图片
function nextImage() {
  if (currentIndex.value < galleryStore.currentGallery.images.length - 1) {
    selectImage(currentIndex.value + 1)
  }
}

// 滚动到指定缩略图
function scrollToThumbnail(index) {
  nextTick(() => {
    if (thumbnailContainer.value) {
      const thumbnailItems = thumbnailContainer.value.children
      if (thumbnailItems[index]) {
        thumbnailItems[index].scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        })
      }
    }
  })
}

// 键盘事件处理
function handleKeydown(event) {
  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      previousImage()
      break
    case 'ArrowRight':
      event.preventDefault()
      nextImage()
      break
    case 'Escape':
      event.preventDefault()
      goBack()
      break
  }
}

// 主图加载成功
function onMainImageLoad() {
  console.log('主图加载成功')
}

// 主图加载失败
function onMainImageError() {
  console.error('主图加载失败')
}

// 监听路由变化
watch(() => route.params.xrid, () => {
  if (route.name === 'DetailDesktop') {
    loadDetail()
  }
})

// 监听当前图片变化，自动滚动缩略图
watch(currentIndex, (newIndex) => {
  scrollToThumbnail(newIndex)
})

// 组件挂载
onMounted(() => {
  // 检测设备类型，如果是移动设备则跳转到移动端详情页
  if (isMobileDevice()) {
    const xrid = route.params.xrid
    console.log(`检测到移动设备，从桌面端详情页跳转到移动端详情页: ${xrid}`)
    router.replace({ name: 'DetailMobile', params: { xrid } })
    return
  }

  loadDetail()
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 桌面端详情页样式 */
.detail-desktop {
  min-height: 100vh;
  background: var(--bg-primary);
}

/* 桌面端头部 - 压缩高度 */
.desktop-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-sm) 0;
}

.desktop-header .container {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-info {
  flex: 1;
}

.gallery-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.gallery-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 主要内容 - 减少padding */
.desktop-content {
  padding: var(--spacing-md) 0;
}

/* 图片查看器 */
.image-viewer {
  margin-bottom: var(--spacing-2xl);
}

/* 主图区域 */
.main-image-area {
  position: relative;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
}

.main-image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 75vh;
  min-height: 75vh;
  max-height: 75vh;
  overflow: hidden;
}

.main-image {
  height: 100%;
  width: auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--border-radius);
  display: block;
}

.image-info {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

/* 导航按钮 */
.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 10;
}

.nav-button:hover {
  background: rgba(0, 0, 0, 0.7);
}

.nav-button-prev {
  left: var(--spacing-md);
}

.nav-button-next {
  right: var(--spacing-md);
}

/* 缩略图条 */
.thumbnail-strip {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  overflow-x: auto;
}

.thumbnail-container {
  display: flex;
  gap: var(--spacing-sm);
  min-height: 100px;
}

.thumbnail-item {
  position: relative;
  flex-shrink: 0;
  width: 100px;
  height: 100px;
  border-radius: var(--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2px solid transparent;
}

.thumbnail-item:hover {
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: var(--primary-color);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-number {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 桌面端导航 */
.desktop-navigation {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-2xl);
}

.nav-item h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.nav-card {
  cursor: pointer;
}

.nav-content {
  display: flex;
  gap: var(--spacing-md);
}

.nav-image {
  width: 120px;
  height: 120px;
  border-radius: var(--border-radius);
  flex-shrink: 0;
}

.nav-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.nav-info h4 {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: 1.4;
}
</style>
