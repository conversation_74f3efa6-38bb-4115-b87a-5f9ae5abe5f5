use anyhow::{anyhow, Result};
use regex::Regex;
use reqwest::Client;
use scraper::{Html, Selector};
use std::time::Duration;
use tracing::{debug, info, warn};

use crate::config::Config;
use crate::models::ListItem;



/// 🕷️ 科学级爬虫服务系统
///
/// 特性：
/// - ⚡ 异步高性能爬取
/// - 🎯 智能错误重试
/// - 🔍 精确数据提取
/// - 🛡️ 反爬虫对抗
#[derive(Clone)]
pub struct CrawlerService {
    client: Client,
    config: Config,
    xrid_regex: Regex,
}

impl CrawlerService {
    /// 🚀 创建爬虫服务实例
    ///
    /// 配置高性能HTTP客户端：
    /// - 🔄 自动重试机制
    /// - ⏱️ 智能超时控制
    /// - 🗜️ 自动压缩支持
    /// - 🔐 TLS优化配置
    pub fn new(config: &Config) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.crawler.timeout))
            .user_agent(&config.crawler.user_agent)
            .gzip(true)                    // 启用gzip压缩
            .tcp_keepalive(Duration::from_secs(60))  // TCP保活
            .pool_idle_timeout(Duration::from_secs(90)) // 连接池空闲超时
            .pool_max_idle_per_host(10)    // 每个主机最大空闲连接
            .build()
            .expect("Failed to create HTTP client");

        // 🎯 预编译正则表达式，提升性能
        let xrid_regex = Regex::new(r"/(\d+)\.").expect("Failed to compile xrid regex");

        Self {
            client,
            config: config.clone(),
            xrid_regex,
        }
    }

    /// 🌐 获取代理URL
    ///
    /// 从配置文件获取当前可用的代理URL
    async fn get_proxy_url(&self) -> Result<String> {
        // 🔧 使用配置中的代理前缀和基础URL
        Ok(format!("{}{}", self.config.crawler.proxy_prefix, self.config.crawler.base_url))
    }

    /// 🎯 获取列表页数据 - 核心爬虫逻辑
    ///
    /// 智能爬取策略：
    /// 1. 🌐 动态URL构建
    /// 2. 🔍 精确元素定位
    /// 3. 📊 数据智能提取
    /// 4. ✅ 结果验证过滤
    pub async fn get_list_page(&self, page: i32) -> Result<Vec<ListItem>> {
        info!("🎯 开始爬取第{}页列表数据", page);

        // 🏗️ 智能URL构建
        let page_url = if page == 1 {
            "XiuRen/".to_string()
        } else {
            format!("XiuRen/index{}.html", page)
        };

        let full_url = self.config.build_proxy_url(&page_url);
        info!("🌐 目标URL: {}", full_url);

        // 🚀 发起HTTP请求 - 科学级调试模式
        info!("🔍 发送HTTP请求到: {}", full_url);
        let response = self.client
            .get(&full_url)
            .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
            .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
            // 🎯 关键修复：不手动设置Accept-Encoding，让reqwest自动处理
            .header("Cache-Control", "no-cache")
            .header("Pragma", "no-cache")
            .send()
            .await?;

        // 🔍 响应状态和头部信息检查
        let status = response.status();
        let headers = response.headers().clone();

        info!("📊 响应状态: {}", status);
        if let Some(content_type) = headers.get("content-type") {
            info!("📋 Content-Type: {:?}", content_type);
        }
        if let Some(content_encoding) = headers.get("content-encoding") {
            info!("📋 Content-Encoding: {:?}", content_encoding);
        }
        if let Some(content_length) = headers.get("content-length") {
            info!("📋 Content-Length: {:?}", content_length);
        }

        if !status.is_success() {
            return Err(anyhow!("HTTP请求失败: {}", status));
        }

        // 📄 获取HTML内容 - reqwest会自动处理gzip解压
        let html = response.text().await?;
        info!("📊 HTML内容长度: {} 字符", html.len());
        let document = Html::parse_document(&html);

        // 🎯 调试信息输出
        if let Some(title_element) = document.select(&Selector::parse("title").unwrap()).next() {
            let title = title_element.text().collect::<String>();
            info!("📄 页面标题: {}", title);
        }

        // 🔍 输出HTML内容的前1000个字符用于调试 (安全字符边界处理)
        let html_preview = if html.len() > 1000 {
            // 安全地截取字符串，避免字符边界问题
            html.chars().take(1000).collect::<String>()
        } else {
            html.clone()
        };
        info!("📄 HTML内容预览: {}", html_preview);

        // 🔍 调试：检查页面结构 - 参考Go/Node.js版本
        let i_list_selector = Selector::parse(".i_list").unwrap();
        let list_n2_selector = Selector::parse(".list_n2").unwrap();
        let combined_selector = Selector::parse(".i_list.list_n2").unwrap();

        let i_list_count = document.select(&i_list_selector).count();
        let list_n2_count = document.select(&list_n2_selector).count();
        let combined_count = document.select(&combined_selector).count();

        info!("🔍 找到的 .i_list 元素数量: {}", i_list_count);
        info!("🔍 找到的 .list_n2 元素数量: {}", list_n2_count);
        info!("🔍 找到的 .i_list.list_n2 元素数量: {}", combined_count);

        // 🔍 精确元素定位 - 尝试多种选择器策略
        let selector = Selector::parse(".i_list.list_n2")
            .map_err(|e| anyhow!("CSS选择器解析失败: {:?}", e))?;

        let mut items = Vec::new();
        let mut processed_count = 0;

        // 🕷️ 遍历所有匹配元素
        for (index, element) in document.select(&selector).enumerate() {
            processed_count += 1;

            match self.extract_list_item(element, index + 1) {
                Ok(Some(item)) => {
                    debug!("✅ 提取成功 #{}: xrid={}, title={}", index + 1, item.xrid, item.title);
                    items.push(item);
                }
                Ok(None) => {
                    debug!("⚠️  跳过无效项 #{}", index + 1);
                }
                Err(e) => {
                    warn!("❌ 提取失败 #{}: {}", index + 1, e);
                }
            }
        }

        info!("🎉 第{}页爬取完成: 处理{}个元素，提取{}条有效记录",
              page, processed_count, items.len());

        if items.is_empty() && processed_count == 0 {
            warn!("⚠️  未找到任何 .i_list.list_n2 元素，可能页面结构已变化");
        }

        Ok(items)
    }

    /// 🎯 提取单个列表项数据
    ///
    /// 智能数据提取：
    /// - 🖼️ 图片URL智能获取
    /// - 📝 标题文本清理
    /// - 🔗 链接地址提取
    /// - 🔢 ID正则匹配
    fn extract_list_item(&self, element: scraper::ElementRef, index: usize) -> Result<Option<ListItem>> {
        // 🔍 查找图片元素
        let img_selector = Selector::parse("img").unwrap();
        let img_element = element.select(&img_selector).next()
            .ok_or_else(|| anyhow!("未找到img元素"))?;

        // 🖼️ 获取图片URL - 优先data-original，备选src
        let fm = img_element.value().attr("data-original")
            .or_else(|| img_element.value().attr("src"))
            .ok_or_else(|| anyhow!("未找到图片URL"))?;

        // 🔗 查找链接元素
        let a_selector = Selector::parse("a").unwrap();
        let a_element = element.select(&a_selector).next()
            .ok_or_else(|| anyhow!("未找到a元素"))?;

        let href = a_element.value().attr("href")
            .ok_or_else(|| anyhow!("未找到href属性"))?;

        // 📝 查找标题元素
        let title_selector = Selector::parse(".list_n2_title, .meta-title").unwrap();
        let title_element = element.select(&title_selector).next()
            .ok_or_else(|| anyhow!("未找到标题元素"))?;

        let title = title_element.text().collect::<String>().trim().to_string();

        // 🔢 提取xrid
        let xrid = self.extract_xrid_from_url(fm)
            .or_else(|| self.extract_xrid_from_url(href))
            .ok_or_else(|| anyhow!("无法提取xrid"))?;

        // ✅ 数据验证
        if title.is_empty() {
            return Ok(None);
        }

        debug!("🔍 提取详情 #{}: xrid={}, fm={}, title={}, url={}",
               index, xrid, fm, title, href);

        Ok(Some(ListItem {
            xrid,
            fm: fm.to_string(),
            title,
            url: href.to_string(),
        }))
    }

    /// 🔢 从URL中提取xrid
    ///
    /// 使用预编译正则表达式，性能优化
    fn extract_xrid_from_url(&self, url: &str) -> Option<i32> {
        self.xrid_regex
            .captures(url)?
            .get(1)?
            .as_str()
            .parse()
            .ok()
    }

    /// 🖼️ 获取详情页图片列表
    ///
    /// 爬取指定URL的详情页，提取所有图片链接
    /// 支持多页面自动翻页
    pub async fn get_detail_images(&self, url: &str) -> Result<Vec<String>> {
        info!("🚀 开始爬取详情页图片: {}", url);

        let mut all_images = Vec::new();

        // 🌐 获取代理URL
        let proxy_url = self.get_proxy_url().await?;
        let full_url = format!("{}{}", proxy_url, url);

        info!("🔍 获取第一页: {}", full_url);

        // 🚀 发起HTTP请求获取第一页
        let response = self.client
            .get(&full_url)
            .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
            .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
            .header("Cache-Control", "no-cache")
            .header("Pragma", "no-cache")
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow!("HTTP请求失败: {}", response.status()));
        }

        let html = response.text().await?;

        // 🖼️ 提取第一页图片和分页链接 - 在一个作用域内完成，避免跨await边界
        let (first_page_images, page_links) = {
            let document = Html::parse_document(&html);
            let img_selector = Selector::parse("div.content div.content_left img").unwrap();

            let mut first_images = Vec::new();
            for element in document.select(&img_selector) {
                if let Some(img_src) = element.value().attr("data-original")
                    .or_else(|| element.value().attr("src")) {
                    if img_src.contains("uploadfile") {
                        first_images.push(img_src.to_string());
                        info!("📸 第1页图片: {}", img_src);
                    }
                }
            }

            let page_links = self.extract_page_links(&document);
            (first_images, page_links)
        };

        all_images.extend(first_page_images);
        let page_links_count = page_links.len();
        info!("🔗 发现{}个分页链接", page_links_count);

        // 🔄 处理其他页面
        for page_link in page_links {
            info!("📄 处理第{}页: {}", page_link.page, page_link.url);

            let page_url = format!("{}{}", proxy_url, page_link.url);

            match self.client
                .get(&page_url)
                .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
                .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                .header("Cache-Control", "no-cache")
                .header("Pragma", "no-cache")
                .send()
                .await {
                Ok(page_response) => {
                    if page_response.status().is_success() {
                        let page_html = page_response.text().await?;

                        // 🖼️ 提取该页图片 - 在作用域内完成HTML解析
                        let page_images = {
                            let page_document = Html::parse_document(&page_html);
                            let img_selector = Selector::parse("div.content div.content_left img").unwrap();

                            let mut images = Vec::new();
                            for element in page_document.select(&img_selector) {
                                if let Some(img_src) = element.value().attr("data-original")
                                    .or_else(|| element.value().attr("src")) {
                                    if img_src.contains("uploadfile") {
                                        images.push(img_src.to_string());
                                        info!("📸 第{}页图片: {}", page_link.page, img_src);
                                    }
                                }
                            }
                            images
                        };

                        all_images.extend(page_images);
                    }
                }
                Err(e) => {
                    warn!("⚠️  第{}页请求失败: {}", page_link.page, e);
                }
            }

            // 添加延迟避免请求过快
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
        }

        info!("✅ 详情页爬取完成，共获取{}张图片", all_images.len());
        Ok(all_images)
    }

    /// 🔗 提取分页链接
    ///
    /// 从HTML文档中提取所有分页链接
    fn extract_page_links(&self, document: &Html) -> Vec<PageLink> {
        let mut page_links = Vec::new();

        // 查找分页链接的选择器
        let page_selector = Selector::parse("div.content div.content_left div.page a").unwrap();

        for element in document.select(&page_selector) {
            if let Some(href) = element.value().attr("href") {
                let text = element.text().collect::<String>().trim().to_string();

                // 尝试解析页码
                if let Ok(page_num) = text.parse::<i32>() {
                    if page_num > 1 { // 跳过第1页（已经处理过）
                        page_links.push(PageLink {
                            page: text,
                            url: href.to_string(),
                        });
                    }
                }
            }
        }

        // 按页码排序
        page_links.sort_by(|a, b| {
            let a_num = a.page.parse::<i32>().unwrap_or(0);
            let b_num = b.page.parse::<i32>().unwrap_or(0);
            a_num.cmp(&b_num)
        });

        page_links
    }
}

/// 🔗 分页链接结构
#[derive(Debug)]
struct PageLink {
    page: String,
    url: String,
}

// 🚀 导入上传服务模块
pub mod upload;
pub use upload::*;

// 🕐 导入定时任务服务模块
pub mod cron;
pub use cron::*;
