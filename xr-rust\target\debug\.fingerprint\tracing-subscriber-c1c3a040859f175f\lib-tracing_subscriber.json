{"rustc": 3926191382657067107, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 7161640089466910437, "path": 17289144815644138609, "deps": [[1009387600818341822, "matchers", false, 14947367410842391667], [1017461770342116999, "sharded_slab", false, 12925209231150527706], [1359731229228270592, "thread_local", false, 14862661718951466475], [3424551429995674438, "tracing_core", false, 12512071786702962337], [3666196340704888985, "smallvec", false, 5900796941273432104], [3722963349756955755, "once_cell", false, 7058571041240436426], [6981130804689348050, "tracing_serde", false, 3791153074627666858], [8569119365930580996, "serde_json", false, 5052918238847929435], [8606274917505247608, "tracing", false, 7681878162268537011], [8614575489689151157, "nu_ansi_term", false, 8100086957489052460], [9451456094439810778, "regex", false, 10464008331717325045], [9689903380558560274, "serde", false, 8030539162158513049], [10806489435541507125, "tracing_log", false, 4750892682870188554]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-c1c3a040859f175f\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}