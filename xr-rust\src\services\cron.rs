use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio_cron_scheduler::{Job, JobScheduler};
use tracing::{error, info, warn};

/// 🕐 定时任务服务 - Rust科学级定时任务实现
///
/// 功能特性：
/// - 🔄 7个完整定时任务
/// - 🛡️ 内存安全保证
/// - ⚡ 异步高性能处理
/// - 🔒 任务状态保护
/// - 🎯 优雅关闭支持
#[derive(Clone)]
pub struct CronService {
    scheduler: JobScheduler,
    base_url: String,
    client: reqwest::Client,

    // 处理状态跟踪，确保串行处理
    processing_states: Arc<RwLock<ProcessingStates>>,
}

/// 📊 处理状态跟踪结构
#[derive(Debug, Default)]
struct ProcessingStates {
    is_processing_reurl: bool,
    is_processing_refm: bool,
    is_processing_imglist: bool,
    is_processing_list: bool,
    is_processing_cleanup: bool,
    is_processing_url_check: bool,
    is_processing_retry: bool,
}

impl CronService {
    /// 🏗️ 创建新的定时任务服务实例
    pub async fn new(port: u16) -> Result<Self> {
        let scheduler = JobScheduler::new().await?;
        let base_url = format!("http://127.0.0.1:{}", port);

        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(60))
            .build()?;

        Ok(Self {
            scheduler,
            base_url,
            client,
            processing_states: Arc::new(RwLock::new(ProcessingStates::default())),
        })
    }

    /// 🚀 启动所有定时任务
    pub async fn start(&self) -> Result<()> {
        info!("🕐 启动Rust定时任务系统...");

        // 1. 每10秒处理一张内页图片reurl (串行处理)
        self.setup_reurl_task().await?;

        // 2. 每30秒处理一张封面图片refm (串行处理)
        self.setup_refm_task().await?;

        // 3. 每60秒爬取内页图片
        self.setup_imglist_task().await?;

        // 4. 每10分钟获取列表页
        self.setup_list_task().await?;

        // 5. 每3小时清理无效数据
        self.setup_cleanup_task().await?;

        // 6. 每5分钟检查域名变更
        self.setup_url_check_task().await?;

        // 7. 每15分钟重试失败的上传
        self.setup_retry_task().await?;

        // 启动调度器
        self.scheduler.start().await?;

        info!("✅ Rust定时任务系统启动完成 - 7个任务已激活");
        Ok(())
    }

    /// 🛑 停止所有定时任务
    pub async fn stop(&mut self) -> Result<()> {
        info!("🛑 停止Rust定时任务系统...");
        self.scheduler.shutdown().await?;
        info!("✅ Rust定时任务系统已停止");
        Ok(())
    }

    /// 🖼️ 设置reurl任务 - 每10秒处理一张内页图片上传
    async fn setup_reurl_task(&self) -> Result<()> {
        let base_url = self.base_url.clone();
        let client = self.client.clone();
        let states = self.processing_states.clone();

        let job = Job::new_async("*/10 * * * * *", move |_uuid, _l| {
            let base_url = base_url.clone();
            let client = client.clone();
            let states = states.clone();

            Box::pin(async move {
                // 检查是否正在处理
                {
                    let mut states_guard = states.write().await;
                    if states_guard.is_processing_reurl {
                        return;
                    }
                    states_guard.is_processing_reurl = true;
                }

                // 执行任务
                let url = format!("{}/reurl", base_url);
                match client.get(&url).send().await {
                    Ok(_) => {
                        // 静默处理成功，避免日志污染
                    }
                    Err(_) => {
                        // 静默处理错误，避免日志污染
                    }
                }

                // 重置处理状态
                {
                    let mut states_guard = states.write().await;
                    states_guard.is_processing_reurl = false;
                }
            })
        })?;

        self.scheduler.add(job).await?;
        info!("📸 reurl任务已设置 - 每10秒执行");
        Ok(())
    }

    /// 🎨 设置refm任务 - 每30秒处理一张封面图片上传
    async fn setup_refm_task(&self) -> Result<()> {
        let base_url = self.base_url.clone();
        let client = self.client.clone();
        let states = self.processing_states.clone();

        let job = Job::new_async("*/30 * * * * *", move |_uuid, _l| {
            let base_url = base_url.clone();
            let client = client.clone();
            let states = states.clone();

            Box::pin(async move {
                // 检查是否正在处理
                {
                    let mut states_guard = states.write().await;
                    if states_guard.is_processing_refm {
                        return;
                    }
                    states_guard.is_processing_refm = true;
                }

                // 执行任务
                let url = format!("{}/refmurl", base_url);
                match client.get(&url).send().await {
                    Ok(_) => {
                        // 静默处理成功
                    }
                    Err(_) => {
                        // 静默处理错误
                    }
                }

                // 重置处理状态
                {
                    let mut states_guard = states.write().await;
                    states_guard.is_processing_refm = false;
                }
            })
        })?;

        self.scheduler.add(job).await?;
        info!("🎨 refm任务已设置 - 每30秒执行");
        Ok(())
    }

    /// 🖼️ 设置imglist任务 - 每60秒爬取内页图片
    async fn setup_imglist_task(&self) -> Result<()> {
        let base_url = self.base_url.clone();
        let client = self.client.clone();
        let states = self.processing_states.clone();

        let job = Job::new_async("0 * * * * *", move |_uuid, _l| {
            let base_url = base_url.clone();
            let client = client.clone();
            let states = states.clone();

            Box::pin(async move {
                // 检查是否正在处理
                {
                    let mut states_guard = states.write().await;
                    if states_guard.is_processing_imglist {
                        return;
                    }
                    states_guard.is_processing_imglist = true;
                }

                // 执行任务
                let url = format!("{}/getimglist", base_url);
                match client.get(&url).send().await {
                    Ok(_) => {
                        // 静默处理成功
                    }
                    Err(_) => {
                        // 静默处理错误
                    }
                }

                // 重置处理状态
                {
                    let mut states_guard = states.write().await;
                    states_guard.is_processing_imglist = false;
                }
            })
        })?;

        self.scheduler.add(job).await?;
        info!("🖼️ imglist任务已设置 - 每60秒执行");
        Ok(())
    }

    /// 📋 设置list任务 - 每10分钟获取列表页
    async fn setup_list_task(&self) -> Result<()> {
        let base_url = self.base_url.clone();
        let client = self.client.clone();
        let states = self.processing_states.clone();

        let job = Job::new_async("0 */10 * * * *", move |_uuid, _l| {
            let base_url = base_url.clone();
            let client = client.clone();
            let states = states.clone();

            Box::pin(async move {
                // 检查是否正在处理
                {
                    let mut states_guard = states.write().await;
                    if states_guard.is_processing_list {
                        return;
                    }
                    states_guard.is_processing_list = true;
                }

                // 执行任务
                let url = format!("{}/getlist", base_url);
                match client.get(&url).send().await {
                    Ok(_) => {
                        info!("📋 执行列表获取任务");
                    }
                    Err(e) => {
                        error!("❌ 列表获取任务失败: {}", e);
                    }
                }

                // 重置处理状态
                {
                    let mut states_guard = states.write().await;
                    states_guard.is_processing_list = false;
                }
            })
        })?;

        self.scheduler.add(job).await?;
        info!("📋 list任务已设置 - 每10分钟执行");
        Ok(())
    }

    /// 🧹 设置cleanup任务 - 每3小时清理无效数据
    async fn setup_cleanup_task(&self) -> Result<()> {
        let base_url = self.base_url.clone();
        let client = self.client.clone();
        let states = self.processing_states.clone();

        let job = Job::new_async("0 0 */3 * * *", move |_uuid, _l| {
            let base_url = base_url.clone();
            let client = client.clone();
            let states = states.clone();

            Box::pin(async move {
                // 检查是否正在处理
                {
                    let mut states_guard = states.write().await;
                    if states_guard.is_processing_cleanup {
                        return;
                    }
                    states_guard.is_processing_cleanup = true;
                }

                // 执行清理任务
                let url = format!("{}/upnull", base_url);
                match client.get(&url).send().await {
                    Ok(_) => {
                        info!("🧹 执行数据清理任务");
                    }
                    Err(e) => {
                        error!("❌ 数据清理任务失败: {}", e);
                    }
                }

                // 重置处理状态
                {
                    let mut states_guard = states.write().await;
                    states_guard.is_processing_cleanup = false;
                }
            })
        })?;

        self.scheduler.add(job).await?;
        info!("🧹 cleanup任务已设置 - 每3小时执行");
        Ok(())
    }

    /// 🔄 设置URL检查任务 - 每5分钟检查域名变更
    async fn setup_url_check_task(&self) -> Result<()> {
        let states = self.processing_states.clone();

        let job = Job::new_async("0 */5 * * * *", move |_uuid, _l| {
            let states = states.clone();

            Box::pin(async move {
                // 检查是否正在处理
                {
                    let mut states_guard = states.write().await;
                    if states_guard.is_processing_url_check {
                        return;
                    }
                    states_guard.is_processing_url_check = true;
                }

                // 执行域名检查任务
                match Self::check_and_update_base_url().await {
                    Ok(_) => {
                        info!("🔄 域名检查完成");
                    }
                    Err(e) => {
                        error!("❌ 域名检查任务失败: {}", e);
                    }
                }

                // 重置处理状态
                {
                    let mut states_guard = states.write().await;
                    states_guard.is_processing_url_check = false;
                }
            })
        })?;

        self.scheduler.add(job).await?;
        info!("🔄 URL检查任务已设置 - 每5分钟执行");
        Ok(())
    }

    /// 🔄 设置重试任务 - 每15分钟重试失败的上传
    async fn setup_retry_task(&self) -> Result<()> {
        let base_url = self.base_url.clone();
        let client = self.client.clone();
        let states = self.processing_states.clone();

        let job = Job::new_async("0 */15 * * * *", move |_uuid, _l| {
            let base_url = base_url.clone();
            let client = client.clone();
            let states = states.clone();

            Box::pin(async move {
                // 检查是否正在处理
                {
                    let mut states_guard = states.write().await;
                    if states_guard.is_processing_retry {
                        return;
                    }
                    states_guard.is_processing_retry = true;
                }

                // 执行重试任务
                let url = format!("{}/reurl/retry?codes=4500,4503,4040&limit=10", base_url);
                match client.get(&url).send().await {
                    Ok(response) => {
                        if let Ok(text) = response.text().await {
                            if let Ok(json) = serde_json::from_str::<serde_json::Value>(&text) {
                                if let Some(processed_count) = json.get("processedCount") {
                                    info!("🔄 重试任务完成: 处理了{}条失败记录", processed_count);
                                } else {
                                    info!("🔄 重试任务完成: 没有需要重试的记录");
                                }
                            } else {
                                info!("🔄 重试任务完成");
                            }
                        }
                    }
                    Err(e) => {
                        error!("❌ 重试任务失败: {}", e);
                    }
                }

                // 重置处理状态
                {
                    let mut states_guard = states.write().await;
                    states_guard.is_processing_retry = false;
                }
            })
        })?;

        self.scheduler.add(job).await?;
        info!("🔄 重试任务已设置 - 每15分钟执行");
        Ok(())
    }

    /// 🌐 检查并更新基础URL
    async fn check_and_update_base_url() -> Result<()> {
        // 尝试读取finalUrl.txt文件
        let content = match tokio::fs::read_to_string("finalUrl.txt").await {
            Ok(content) => content,
            Err(_) => {
                // 如果文件不存在，创建默认文件
                let default_url = "https://www.xiu01.top/";
                tokio::fs::write("finalUrl.txt", default_url).await?;
                info!("📝 创建默认URL文件: {}", default_url);
                return Ok(());
            }
        };

        let base_url = content.trim();
        if base_url.is_empty() {
            return Err(anyhow::anyhow!("URL文件为空"));
        }

        // 简单的HTTP检查
        let test_url = if base_url.starts_with("http") {
            base_url.to_string()
        } else {
            format!("https://{}", base_url)
        };

        // 创建不跟随重定向的客户端
        let client = reqwest::Client::builder()
            .redirect(reqwest::redirect::Policy::none())
            .timeout(std::time::Duration::from_secs(15))
            .build()?;

        match client.get(&test_url).send().await {
            Ok(response) => {
                let status = response.status();

                // 检查是否有重定向
                if status.is_redirection() {
                    if let Some(location) = response.headers().get("location") {
                        if let Ok(new_url) = location.to_str() {
                            if new_url != base_url {
                                // 更新文件中的URL
                                match tokio::fs::write("finalUrl.txt", new_url).await {
                                    Ok(_) => {
                                        info!("🎉 域名已更新: {} -> {}", base_url, new_url);
                                    }
                                    Err(e) => {
                                        warn!("❌ 更新URL文件失败: {}", e);
                                    }
                                }
                            } else {
                                info!("✅ 域名无变化: {}", base_url);
                            }
                        }
                    }
                } else if status.is_success() {
                    info!("✅ 域名检查成功: {}", base_url);
                } else {
                    warn!("⚠️  域名响应异常: {} - HTTP {}", base_url, status);
                }
            }
            Err(e) => {
                warn!("⚠️  域名检查失败: {} - {}", base_url, e);
                return Err(anyhow::anyhow!("域名检查失败: {}", e));
            }
        }

        Ok(())
    }
}
