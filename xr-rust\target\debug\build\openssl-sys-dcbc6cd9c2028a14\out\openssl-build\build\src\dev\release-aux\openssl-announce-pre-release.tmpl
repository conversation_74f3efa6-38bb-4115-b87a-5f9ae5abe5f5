
   OpenSSL version $release_text released
   ======================================

   OpenSSL - The Open Source toolkit for SSL/TLS
   https://www.openssl.org/

   OpenSSL $series is currently in $label.

   OpenSSL $release_text has now been made available.

   Note: This OpenSSL pre-release has been provided for testing ONLY.
   It should NOT be used for security critical purposes.

   Specific notes on upgrading to OpenSSL $series from previous versions are
   available in the OpenSSL Migration Guide, here:

        https://www.openssl.org/docs/manmaster/man7/ossl-guide-migration.html

   The $label release is available for download via HTTPS and FTP from the
   following master locations (you can find the various FTP mirrors under
   https://www.openssl.org/source/mirror.html):

     * https://www.openssl.org/source/
     * ftp://ftp.openssl.org/source/

   The distribution file name is:

    o $tarfile
      Size: $length
      SHA1 checksum: $sha1hash
      SHA256 checksum: $sha256hash

   The checksums were calculated using the following commands:

    openssl sha1 $tarfile
    openssl sha256 $tarfile

   Please download and check this $label release as soon as possible.
   To report a bug, open an issue on GitHub:

    https://github.com/openssl/openssl/issues

   Please check the release notes and mailing lists to avoid duplicate
   reports of known issues. (Of course, the source is also available
   on GitHub.)

   Yours,

   The OpenSSL Project Team.

