import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { galleryApi } from '@/utils/api'

export const useGalleryStore = defineStore('gallery', () => {
  // 状态
  const galleries = ref([])
  const currentGallery = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 分页状态
  const pagination = ref({
    currentPage: 1,
    perPage: 15,
    total: 0,
    totalPages: 0
  })
  
  // 排序状态
  const sortOrder = ref('latest') // 'latest' | 'oldest'
  
  // 计算属性
  const hasGalleries = computed(() => galleries.value.length > 0)
  const hasMore = computed(() => pagination.value.currentPage < pagination.value.totalPages)
  
  // 获取图库列表
  async function fetchGalleries(params = {}) {
    loading.value = true
    error.value = null
    
    try {
      const requestParams = {
        page: pagination.value.currentPage || 1,
        limit: pagination.value.perPage || 15,
        sort: sortOrder.value || 'latest',
        ...params
      }
      
      const response = await galleryApi.getList(requestParams)

      galleries.value = response.data.list

      // 安全地更新分页信息，处理后端下划线命名到前端驼峰命名的转换
      if (response.data.pagination) {
        const backendPagination = response.data.pagination
        pagination.value = {
          currentPage: backendPagination.current_page || backendPagination.currentPage || pagination.value.currentPage || 1,
          perPage: backendPagination.per_page || backendPagination.perPage || pagination.value.perPage || 15,
          total: backendPagination.total || 0,
          totalPages: backendPagination.total_pages || backendPagination.totalPages || 0
        }


      }
      
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('获取图库列表失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 获取图库详情
  async function fetchGalleryDetail(xrid) {
    loading.value = true
    error.value = null
    
    try {
      const response = await galleryApi.getDetail(xrid)
      currentGallery.value = response.data
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('获取图库详情失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 切换排序
  function toggleSort() {
    sortOrder.value = sortOrder.value === 'latest' ? 'oldest' : 'latest'
    pagination.value.currentPage = 1 // 重置到第一页
    return fetchGalleries()
  }
  
  // 跳转到指定页面
  function goToPage(page) {
    if (page >= 1 && page <= pagination.value.totalPages) {
      pagination.value.currentPage = page
      return fetchGalleries()
    }
  }
  
  // 下一页
  function nextPage() {
    if (hasMore.value) {
      return goToPage(pagination.value.currentPage + 1)
    }
  }
  
  // 上一页
  function prevPage() {
    if (pagination.value.currentPage > 1) {
      return goToPage(pagination.value.currentPage - 1)
    }
  }

  // 改变每页数量
  function changePageSize(pageSize) {
    pagination.value.perPage = pageSize
    pagination.value.currentPage = 1 // 重置到第一页
    return fetchGalleries()
  }
  
  // 重置状态
  function reset() {
    galleries.value = []
    currentGallery.value = null
    loading.value = false
    error.value = null
    pagination.value = {
      currentPage: 1,
      perPage: 15,
      total: 0,
      totalPages: 0
    }
    sortOrder.value = 'latest'
  }
  
  return {
    // 状态
    galleries,
    currentGallery,
    loading,
    error,
    pagination,
    sortOrder,
    
    // 计算属性
    hasGalleries,
    hasMore,
    
    // 方法
    fetchGalleries,
    fetchGalleryDetail,
    toggleSort,
    goToPage,
    nextPage,
    prevPage,
    changePageSize,
    reset
  }
})
