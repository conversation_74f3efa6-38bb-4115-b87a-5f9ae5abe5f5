import { test, expect } from '@playwright/test';

test.describe('XR Gallery Debug Tests', () => {
  test('Check homepage loading and image display', async ({ page }) => {
    // 导航到首页
    await page.goto('/');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 检查页面标题
    await expect(page).toHaveTitle(/XR Gallery/);

    // 检查是否有图库卡片
    const galleryCards = page.locator('.gallery-item');
    await expect(galleryCards.first()).toBeVisible({ timeout: 10000 });

    // 检查图片加载状态
    const firstImage = page.locator('.gallery-image').first();
    const firstPlaceholder = page.locator('.image-placeholder').first();
    const firstLoadingOverlay = page.locator('.loading-overlay').first();

    console.log('Image visible:', await firstImage.isVisible());
    console.log('Placeholder visible:', await firstPlaceholder.isVisible());
    console.log('Loading overlay visible:', await firstLoadingOverlay.isVisible());

    // 等待图片加载完成或超时
    try {
      await firstImage.waitFor({ state: 'visible', timeout: 15000 });
      console.log('Image loaded successfully');
    } catch (error) {
      console.log('Image loading timeout:', error.message);
    }

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/homepage-images.png', fullPage: true });
  });
  
  test('Check API response encoding', async ({ page }) => {
    // 监听网络请求
    const apiResponses = [];
    
    page.on('response', async (response) => {
      if (response.url().includes('/api/gallery/list')) {
        const contentType = response.headers()['content-type'];
        const responseText = await response.text();
        
        apiResponses.push({
          url: response.url(),
          status: response.status(),
          contentType,
          bodyPreview: responseText.substring(0, 200)
        });
        
        console.log('API Response:', {
          url: response.url(),
          status: response.status(),
          contentType,
          bodyPreview: responseText.substring(0, 200)
        });
      }
    });
    
    // 导航到首页触发API请求
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 验证API响应
    expect(apiResponses.length).toBeGreaterThan(0);
    
    const apiResponse = apiResponses[0];
    expect(apiResponse.status).toBe(200);
    expect(apiResponse.contentType).toContain('application/json');
    expect(apiResponse.contentType).toContain('charset=utf-8');
  });
  
  test('Test device detection and routing', async ({ page }) => {
    // 导航到首页
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 点击第一个图库卡片
    const firstCard = page.locator('.gallery-item').first();
    await expect(firstCard).toBeVisible({ timeout: 10000 });

    // 监听控制台日志，查看设备检测结果
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.text().includes('设备类型检测')) {
        consoleMessages.push(msg.text());
      }
    });

    await firstCard.click();

    // 等待详情页加载
    await page.waitForLoadState('networkidle');

    // 检查URL是否包含detail
    expect(page.url()).toContain('/detail/');

    // 检查设备检测日志
    console.log('设备检测日志:', consoleMessages);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/detail-page.png', fullPage: true });
  });

  test('Test mobile waterfall layout', async ({ page }) => {
    // 模拟移动设备
    await page.setViewportSize({ width: 375, height: 667 });

    // 直接导航到移动端详情页
    await page.goto('/mobile/detail/17735');
    await page.waitForLoadState('networkidle');

    // 检查瀑布流容器
    const waterfallContainer = page.locator('.waterfall-container');
    await expect(waterfallContainer).toBeVisible({ timeout: 10000 });

    // 检查图片宽度是否等于屏幕宽度
    const firstImage = page.locator('.waterfall-image').first();
    await expect(firstImage).toBeVisible({ timeout: 10000 });

    // 获取图片和屏幕宽度
    const imageWidth = await firstImage.evaluate(img => img.offsetWidth);
    const screenWidth = await page.evaluate(() => window.innerWidth);

    console.log(`图片宽度: ${imageWidth}px, 屏幕宽度: ${screenWidth}px`);

    // 验证图片宽度等于屏幕宽度
    expect(imageWidth).toBe(screenWidth);

    // 检查页面是否禁止水平滚动
    const bodyOverflowX = await page.evaluate(() =>
      window.getComputedStyle(document.body).overflowX
    );
    console.log(`Body overflow-x: ${bodyOverflowX}`);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/mobile-waterfall.png', fullPage: true });
  });

  test('Test desktop main image height fitting', async ({ page }) => {
    // 设置桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 });

    // 直接导航到桌面端详情页
    await page.goto('/desktop/detail/17735');
    await page.waitForLoadState('networkidle');

    // 检查主图容器
    const mainImageContainer = page.locator('.main-image-container');
    await expect(mainImageContainer).toBeVisible({ timeout: 10000 });

    // 检查主图
    const mainImage = page.locator('.main-image');
    await expect(mainImage).toBeVisible({ timeout: 10000 });

    // 获取容器和图片的尺寸
    const containerHeight = await mainImageContainer.evaluate(el => el.offsetHeight);
    const imageHeight = await mainImage.evaluate(img => img.offsetHeight);
    const imageWidth = await mainImage.evaluate(img => img.offsetWidth);

    console.log(`容器高度: ${containerHeight}px`);
    console.log(`图片高度: ${imageHeight}px`);
    console.log(`图片宽度: ${imageWidth}px`);

    // 验证图片高度等于容器高度（允许1px的误差）
    expect(Math.abs(imageHeight - containerHeight)).toBeLessThanOrEqual(1);

    // 验证容器高度应该更高（约75vh）
    const viewportHeight = await page.evaluate(() => window.innerHeight);
    const expectedHeight = Math.floor(viewportHeight * 0.75);
    console.log(`视口高度: ${viewportHeight}px, 期望容器高度: ${expectedHeight}px`);
    expect(Math.abs(containerHeight - expectedHeight)).toBeLessThanOrEqual(5);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/desktop-main-image.png', fullPage: true });
  });

  test('Test device auto-redirect from desktop URL to mobile', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });

    // 监听控制台日志
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.text().includes('检测到移动设备')) {
        consoleMessages.push(msg.text());
      }
    });

    // 直接访问桌面端详情页URL
    await page.goto('/desktop/detail/17735');
    await page.waitForLoadState('networkidle');

    // 应该自动跳转到移动端详情页
    expect(page.url()).toContain('/mobile/detail/17735');

    // 检查是否有瀑布流容器
    const waterfallContainer = page.locator('.waterfall-container');
    await expect(waterfallContainer).toBeVisible({ timeout: 10000 });

    console.log('设备检测跳转日志:', consoleMessages);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/auto-redirect-mobile.png', fullPage: true });
  });

  test('Test device auto-redirect from mobile URL to desktop', async ({ page }) => {
    // 设置桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 });

    // 监听控制台日志
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.text().includes('检测到桌面设备')) {
        consoleMessages.push(msg.text());
      }
    });

    // 直接访问移动端详情页URL
    await page.goto('/mobile/detail/17735');
    await page.waitForLoadState('networkidle');

    // 应该自动跳转到桌面端详情页
    expect(page.url()).toContain('/desktop/detail/17735');

    // 检查是否有主图容器
    const mainImageContainer = page.locator('.main-image-container');
    await expect(mainImageContainer).toBeVisible({ timeout: 10000 });

    console.log('设备检测跳转日志:', consoleMessages);

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/auto-redirect-desktop.png', fullPage: true });
  });

  test('Test back to home button functionality', async ({ page }) => {
    // 设置桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 });

    // 监听网络请求和错误
    const networkRequests = [];
    const consoleMessages = [];

    page.on('request', request => {
      networkRequests.push({
        url: request.url(),
        method: request.method()
      });
    });

    page.on('response', response => {
      if (!response.ok()) {
        console.log(`HTTP Error: ${response.status()} ${response.url()}`);
      }
    });

    page.on('console', msg => {
      consoleMessages.push(msg.text());
      console.log(`Console: ${msg.text()}`);
    });

    page.on('pageerror', error => {
      console.log(`Page Error: ${error.message}`);
    });

    // 直接访问详情页
    await page.goto('/desktop/detail/17739');
    await page.waitForLoadState('networkidle');

    // 检查页面是否正常加载
    const backButton = page.locator('text=← 返回首页').first();
    await expect(backButton).toBeVisible({ timeout: 10000 });

    console.log('详情页加载完成，准备点击返回按钮');

    // 点击返回首页按钮
    await backButton.click();
    console.log('已点击返回按钮');

    // 等待一下看看有没有跳转
    await page.waitForTimeout(2000);
    console.log(`点击后的URL: ${page.url()}`);

    // 等待页面跳转
    await page.waitForLoadState('networkidle');

    // 检查是否成功跳转到首页
    expect(page.url()).toBe('http://localhost:3331/');

    // 检查首页是否正常显示
    const pageTitle = page.locator('h1:has-text("XR Gallery")');
    await expect(pageTitle).toBeVisible({ timeout: 5000 });

    console.log('网络请求记录:', networkRequests.slice(-5)); // 显示最后5个请求
    console.log('控制台消息:', consoleMessages.slice(-5)); // 显示最后5个消息

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/back-to-home-test.png', fullPage: true });
  });

  test('Test pagination parameters after back to home', async ({ page }) => {
    // 设置桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 });

    // 监听网络请求
    const apiRequests = [];
    page.on('request', request => {
      if (request.url().includes('/api/gallery/list')) {
        apiRequests.push({
          url: request.url(),
          method: request.method()
        });
        console.log(`API请求: ${request.url()}`);
      }
    });

    // 监听响应错误
    page.on('response', response => {
      if (!response.ok() && response.url().includes('/api/gallery/list')) {
        console.log(`API错误: ${response.status()} ${response.url()}`);
      }
    });

    // 先访问首页，确保正常加载
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 检查首页是否正常加载
    const homeTitle = page.locator('h1:has-text("XR Gallery")');
    await expect(homeTitle).toBeVisible({ timeout: 10000 });

    console.log('首页加载完成');

    // 访问详情页
    await page.goto('/desktop/detail/17739');
    await page.waitForLoadState('networkidle');

    // 点击返回首页
    const backButton = page.locator('text=← 返回首页').first();
    await expect(backButton).toBeVisible({ timeout: 10000 });
    await backButton.click();

    // 等待返回首页
    await page.waitForLoadState('networkidle');

    // 检查URL是否正确
    expect(page.url()).toBe('http://localhost:3331/');

    // 检查是否有API错误
    const errorRequests = apiRequests.filter(req =>
      req.url.includes('undefined') ||
      req.url.includes('page=undefined') ||
      req.url.includes('limit=undefined')
    );

    console.log('所有API请求:', apiRequests);
    console.log('错误请求:', errorRequests);

    // 验证没有undefined参数的请求
    expect(errorRequests.length).toBe(0);

    // 验证最后一个API请求包含正确的参数
    if (apiRequests.length > 0) {
      const lastRequest = apiRequests[apiRequests.length - 1];
      expect(lastRequest.url).toMatch(/page=\d+/);
      expect(lastRequest.url).toMatch(/limit=\d+/);
      expect(lastRequest.url).toMatch(/sort=(latest|oldest)/);
    }

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/pagination-params-test.png', fullPage: true });
  });

  test('Test pagination functionality', async ({ page }) => {
    // 设置桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 });

    // 访问首页
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // 等待分页组件加载
    const pagination = page.locator('.n-pagination');
    await expect(pagination).toBeVisible({ timeout: 10000 });

    console.log('分页组件已加载');

    // 点击第2页
    const page2Button = page.locator('button').filter({ hasText: '2' }).first();
    if (await page2Button.isVisible()) {
      console.log('找到第2页按钮，准备点击');
      await page2Button.click();
      await page.waitForLoadState('networkidle');

      console.log('第2页加载成功');
    } else {
      console.log('没有找到第2页按钮，可能总页数不足');
    }

    // 测试每页数量切换
    const pageSizeSelector = page.locator('.n-select').filter({ hasText: /15|30|50/ });
    if (await pageSizeSelector.isVisible()) {
      await pageSizeSelector.click();

      // 选择30个每页
      const option30 = page.locator('.n-base-select-option').filter({ hasText: '30' });
      if (await option30.isVisible()) {
        await option30.click();
        await page.waitForLoadState('networkidle');

        console.log('每页数量切换到30成功');
      }
    }

    // 截图保存
    await page.screenshot({ path: 'tests/screenshots/pagination-functionality.png', fullPage: true });
  });
});
