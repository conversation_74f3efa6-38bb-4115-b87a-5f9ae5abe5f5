<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <header class="page-header">
      <div class="container">
        <h1 class="page-title">XR Gallery</h1>
        <div class="header-actions">
          <n-button @click="testConnection" :loading="testing">
            测试连接
          </n-button>
          <n-button @click="gallery.toggleSort()" :loading="galleryStore.loading">
            排序: {{ galleryStore.sortOrder === 'latest' ? '最新' : '最旧' }}
          </n-button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 加载状态 -->
        <Loading v-if="galleryStore.loading && !galleryStore.hasGalleries" text="加载中..." />

        <!-- 错误状态 -->
        <ErrorMessage
          v-else-if="galleryStore.error"
          :title="galleryStore.error"
          description="请检查网络连接或稍后重试"
          @retry="gallery.loadGalleries"
        />

        <!-- 空状态 -->
        <n-empty
          v-else-if="!galleryStore.hasGalleries && !galleryStore.loading"
          description="暂无图库数据"
          size="large"
        />

        <!-- 图库网格 -->
        <GalleryGrid
          v-else
          :galleries="galleryStore.galleries"
          :loading="galleryStore.loading"
          :sequential-loading="false"
          @item-click="goToDetail"
        />

        <!-- 分页 -->
        <div v-if="galleryStore.hasGalleries" class="pagination-container">
          <n-pagination
            v-model:page="galleryStore.pagination.currentPage"
            :page-size="galleryStore.pagination.perPage"
            :item-count="galleryStore.pagination.total"
            show-size-picker
            show-quick-jumper
            :page-sizes="[15, 30, 50]"
            :disabled="galleryStore.loading"
            @update:page="gallery.goToPage"
            @update:page-size="gallery.changePageSize"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useGalleryStore } from '@/stores/gallery'
import { useGallery } from '@/composables/useGallery'
import { testConnection } from '@/utils/api'
import { getDetailRouteName } from '@/utils/device'
import Loading from '@/components/Loading.vue'
import ErrorMessage from '@/components/ErrorMessage.vue'
import GalleryGrid from '@/components/GalleryGrid.vue'

const router = useRouter()
const message = useMessage()
const galleryStore = useGalleryStore()
const gallery = useGallery()

const testing = ref(false)

// 测试API连接
async function testApi() {
  testing.value = true
  try {
    const response = await testConnection()
    message.success(`连接成功: ${response.message}`)
  } catch (error) {
    message.error(`连接失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

// 跳转到详情页 - 根据设备类型选择对应的详情页
function goToDetail(xrid) {
  const routeName = getDetailRouteName()
  console.log(`设备类型检测: ${routeName}, 跳转到图库 ${xrid}`)

  if (routeName === 'DetailMobile') {
    router.push({ name: 'DetailMobile', params: { xrid } })
  } else {
    router.push({ name: 'DetailDesktop', params: { xrid } })
  }
}

// 组件挂载时初始化
onMounted(() => {
  gallery.initialize()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.page-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.page-header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.main-content {
  padding: var(--spacing-xl) 0;
}



.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xl) 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header .container {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .header-actions {
    justify-content: center;
  }
}
</style>
