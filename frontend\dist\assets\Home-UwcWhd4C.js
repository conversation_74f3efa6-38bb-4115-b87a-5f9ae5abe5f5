import{r as C,c as _,a as m,o as c,b as r,d as S,F as V,e as E,n as O,f,w as v,g as $,h as P,t as z,u as a,N as G,i as j,j as k,k as q}from"./index-BpqYk2C3.js";import{u as x,a as b,_ as D,t as F,L as H,E as A}from"./ErrorMessage-CVNw5bDd.js";import{g as X}from"./device-Bim2jCTP.js";function J(){const t=x(),n=b(),e=C(!1),o=C(0),d=3,l=_(()=>t.hasGalleries),g=_(()=>t.loading),i=_(()=>t.error),y=_(()=>t.galleries),p=_(()=>t.pagination),h=_(()=>t.sortOrder);async function u(s=!1){try{return await t.fetchGalleries(),o.value=0,s&&l.value&&n.success(`加载了 ${y.value.length} 个图库`),!0}catch(w){return console.error("加载图库失败:",w),o.value<d?(o.value++,n.warning(`加载失败，正在重试 (${o.value}/${d})`),setTimeout(()=>{u(s)},1e3*o.value)):n.error("加载图库失败，请检查网络连接"),!1}}async function N(){try{await t.toggleSort(),n.info(`已切换为${h.value==="latest"?"最新":"最旧"}排序`)}catch{n.error("切换排序失败")}}async function T(s){if(s<1||s>p.value.totalPages){n.warning(`页码超出范围 (1-${p.value.totalPages})`);return}try{await t.goToPage(s)}catch{n.error("跳转页面失败")}}async function R(){if(!t.hasMore){n.info("已经是最后一页了");return}try{await t.nextPage()}catch{n.error("加载下一页失败")}}async function B(){if(p.value.currentPage<=1){n.info("已经是第一页了");return}try{await t.prevPage()}catch{n.error("加载上一页失败")}}async function I(s){if(![15,30,50].includes(s)){n.warning("无效的页面大小");return}try{await t.changePageSize(s),n.success(`已切换为每页 ${s} 个`)}catch{n.error("更改页面大小失败")}}async function M(){t.reset(),await u(!0)}async function L(){if(!e.value)try{await u(),e.value=!0}catch(s){console.error("初始化图库失败:",s)}}function U(){t.reset(),e.value=!1,o.value=0}return{hasData:l,isLoading:g,error:i,galleries:y,pagination:p,sortOrder:h,isInitialized:e,retryCount:o,maxRetries:d,loadGalleries:u,toggleSort:N,goToPage:T,nextPage:R,prevPage:B,changePageSize:I,refresh:M,initialize:L,reset:U}}const K={class:"gallery-grid-container"},Q=["onClick"],W={class:"image-container"},Y=["src","alt"],Z={class:"image-count-badge"},ee={class:"gallery-info"},ae=["title"],te={class:"gallery-meta"},ne={key:0,class:"loading-more"},oe={__name:"GalleryGrid",props:{galleries:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["itemClick"],setup(t,{emit:n}){return(e,o)=>{const d=$("n-spin");return c(),m("div",K,[r("div",{class:O(["gallery-grid",{loading:t.loading}])},[(c(!0),m(V,null,E(t.galleries,(l,g)=>(c(),m("div",{key:l.id,class:"gallery-item",onClick:i=>e.$emit("itemClick",l.xrid)},[r("div",W,[r("img",{src:l.cover,alt:l.title,class:"gallery-image",loading:"lazy"},null,8,Y),r("div",Z,[f(a(G),{size:"small",type:"info"},{default:v(()=>[P(z(l.image_count)+"张 ",1)]),_:2},1024)])]),r("div",ee,[r("h3",{class:"gallery-title",title:l.title},z(l.title),9,ae),r("div",te,[f(a(G),{size:"small",type:"default",class:"xrid-tag"},{default:v(()=>[P(" ID: "+z(l.xrid),1)]),_:2},1024)])])],8,Q))),128))],2),t.loading?(c(),m("div",ne,[f(d,{size:"medium"},{description:v(()=>o[0]||(o[0]=[r("span",null,"加载中...",-1)])),_:1})])):S("",!0)])}}},se=D(oe,[["__scopeId","data-v-a30c57b5"]]),ie={class:"home-page"},re={class:"page-header"},le={class:"container"},ce={class:"header-actions"},de={class:"main-content"},ge={class:"container"},ue={key:4,class:"pagination-container"},pe={__name:"Home",setup(t){const n=q();b();const e=x(),o=J(),d=C(!1);function l(g){const i=X();console.log(`设备类型检测: ${i}, 跳转到图库 ${g}`),i==="DetailMobile"?n.push({name:"DetailMobile",params:{xrid:g}}):n.push({name:"DetailDesktop",params:{xrid:g}})}return j(()=>{o.initialize()}),(g,i)=>{const y=$("n-button"),p=$("n-empty"),h=$("n-pagination");return c(),m("div",ie,[r("header",re,[r("div",le,[i[3]||(i[3]=r("h1",{class:"page-title"},"XR Gallery",-1)),r("div",ce,[f(y,{onClick:a(F),loading:d.value},{default:v(()=>i[2]||(i[2]=[P(" 测试连接 ",-1)])),_:1,__:[2]},8,["onClick","loading"]),f(y,{onClick:i[0]||(i[0]=u=>a(o).toggleSort()),loading:a(e).loading},{default:v(()=>[P(" 排序: "+z(a(e).sortOrder==="latest"?"最新":"最旧"),1)]),_:1},8,["loading"])])])]),r("main",de,[r("div",ge,[a(e).loading&&!a(e).hasGalleries?(c(),k(H,{key:0,text:"加载中..."})):a(e).error?(c(),k(A,{key:1,title:a(e).error,description:"请检查网络连接或稍后重试",onRetry:a(o).loadGalleries},null,8,["title","onRetry"])):!a(e).hasGalleries&&!a(e).loading?(c(),k(p,{key:2,description:"暂无图库数据",size:"large"})):(c(),k(se,{key:3,galleries:a(e).galleries,loading:a(e).loading,"sequential-loading":!1,onItemClick:l},null,8,["galleries","loading"])),a(e).hasGalleries?(c(),m("div",ue,[f(h,{page:a(e).pagination.currentPage,"onUpdate:page":[i[1]||(i[1]=u=>a(e).pagination.currentPage=u),a(o).goToPage],"page-size":a(e).pagination.perPage,"item-count":a(e).pagination.total,"show-size-picker":"","show-quick-jumper":"","page-sizes":[15,30,50],disabled:a(e).loading,"onUpdate:pageSize":a(o).changePageSize},null,8,["page","page-size","item-count","disabled","onUpdate:page","onUpdate:pageSize"])])):S("",!0)])])])}}},ve=D(pe,[["__scopeId","data-v-1416d68a"]]);export{ve as default};
