# Docker an ARM64 MUSL build of xr-crawler
FROM rust:1.82

# Install dependencies for cross-compiling to aarch64-unknown-linux-musl
RUN apt-get update && apt-get install -y \
    musl-tools \
    gcc-aarch64-linux-gnu \
    && rm -rf /var/lib/apt/lists/*

# Add the aarch64-unknown-linux-musl target
RUN rustup target add aarch64-unknown-linux-musl

# Set the working directory
WORKDIR /app

# Copy the project files
COPY . .

# Set the linker for the target architecture
ENV CARGO_TARGET_AARCH64_UNKNOWN_LINUX_MUSL_LINKER=aarch64-linux-gnu-gcc

# Build the project for ARM64 MUSL
RUN cargo build --release --target aarch64-unknown-linux-musl

# The final binary will be in /app/target/aarch64-unknown-linux-musl/release/xr-crawler
# You can build this with:
# docker build -t xr-crawler-arm64 -f Dockerfile.arm64 .
CMD ls -la target/aarch64-unknown-linux-musl/release/