import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isAuthenticated = ref(false)
  const user = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 硬编码的账号密码
  const VALID_CREDENTIALS = {
    username: 'ni<PERSON>',
    password: 'nihao'
  }
  
  // localStorage keys
  const AUTH_TOKEN_KEY = 'auth_token'
  const AUTH_USER_KEY = 'auth_user'
  
  // 计算属性
  const isLoggedIn = computed(() => isAuthenticated.value)
  const currentUser = computed(() => user.value)
  
  // 登录方法
  async function login(credentials) {
    loading.value = true
    error.value = null
    
    try {
      const { username, password } = credentials
      
      // 验证硬编码账号密码
      if (username !== VALID_CREDENTIALS.username || password !== VALID_CREDENTIALS.password) {
        throw new Error('用户名或密码错误')
      }
      
      // 模拟异步登录过程
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 生成模拟token
      const token = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const userData = {
        id: 1,
        username: username,
        name: '用户',
        loginTime: new Date().toISOString()
      }
      
      // 设置状态
      isAuthenticated.value = true
      user.value = userData
      
      // 持久化到localStorage
      localStorage.setItem(AUTH_TOKEN_KEY, token)
      localStorage.setItem(AUTH_USER_KEY, JSON.stringify(userData))
      
      console.log('登录成功:', userData)
      return { token, user: userData }
    } catch (err) {
      error.value = err.message
      console.error('登录失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 登出方法
  function logout() {
    loading.value = true
    error.value = null
    
    try {
      // 清除状态
      isAuthenticated.value = false
      user.value = null
      
      // 清除localStorage
      localStorage.removeItem(AUTH_TOKEN_KEY)
      localStorage.removeItem(AUTH_USER_KEY)
      
      console.log('登出成功')
    } catch (err) {
      error.value = err.message
      console.error('登出失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 检查认证状态方法
  function checkAuth() {
    try {
      const token = localStorage.getItem(AUTH_TOKEN_KEY)
      const userData = localStorage.getItem(AUTH_USER_KEY)
      
      if (token && userData) {
        try {
          const parsedUser = JSON.parse(userData)
          isAuthenticated.value = true
          user.value = parsedUser
          console.log('恢复登录状态:', parsedUser)
          return true
        } catch (parseError) {
          console.error('解析用户数据失败:', parseError)
          // 清除无效数据
          localStorage.removeItem(AUTH_TOKEN_KEY)
          localStorage.removeItem(AUTH_USER_KEY)
        }
      }
      
      return false
    } catch (err) {
      error.value = err.message
      console.error('检查认证状态失败:', err)
      return false
    }
  }
  
  // 获取token方法
  function getToken() {
    return localStorage.getItem(AUTH_TOKEN_KEY)
  }
  
  // 重置状态方法
  function reset() {
    isAuthenticated.value = false
    user.value = null
    loading.value = false
    error.value = null
  }
  
  return {
    // 状态
    isAuthenticated,
    user,
    loading,
    error,
    
    // 计算属性
    isLoggedIn,
    currentUser,
    
    // 方法
    login,
    logout,
    checkAuth,
    getToken,
    reset
  }
})
