{"rustc": 3926191382657067107, "features": "[\"add\", \"add_assign\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 2225463790103693989, "path": 13780134052985258483, "deps": [[3060637413840920116, "proc_macro2", false, 2141545387885295045], [4974441333307933176, "syn", false, 13480060149965268283], [17990358020177143287, "quote", false, 8737441731687220448]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-64873eaf1852c9d8\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}