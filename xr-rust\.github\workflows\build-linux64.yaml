name: Build for Linux x86_64 (musl)

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: "x86_64-unknown-linux-musl"

    - name: Cache cargo registry
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/bin/
          ~/.cargo/registry/index/
          ~/.cargo/registry/cache/
          ~/.cargo/git/db/
        key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-registry-

    - name: Cache cargo build
      uses: actions/cache@v4
      with:
        path: target/
        key: ${{ runner.os }}-x86_64-musl-cargo-build-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-x86_64-musl-cargo-build-

    - name: Install musl tools
      run: |
        sudo apt-get update
        sudo apt-get install -y musl-tools

    - name: Build (native)
      run: |
        cargo build --target x86_64-unknown-linux-musl --release
    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: xr-crawler-x64-musl
        path: target/x86_64-unknown-linux-musl/release/xr-crawler
