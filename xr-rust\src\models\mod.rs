use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 🎯 类型安全的数据模型系统
/// 
/// 特性：
/// - 🔒 编译时类型检查
/// - 🔄 自动序列化/反序列化
/// - 📊 数据库映射优化
/// - 🛡️ 内存安全保证

/// 📋 主表模型 - XiuRen记录
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Xr {
    pub id: i64,
    pub xrid: i32,
    pub issave: i8,
    pub fm: Option<String>,
    pub refm: Option<String>,
    pub title: Option<String>,
    pub url: String,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 🖼️ 图片信息模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct XrInfo {
    pub id: i64,
    pub xrid: i32,
    pub ourl: String,
    pub reurl: Option<String>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 📝 列表页数据项 - 爬虫提取的原始数据
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ListItem {
    pub xrid: i32,
    pub fm: String,
    pub title: String,
    pub url: String,
}

/// 📊 API响应模型 - 统一的响应格式
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub page: Option<i32>,
    pub count: Option<usize>,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    /// ✅ 创建成功响应（带分页信息）
    pub fn success_with_page(data: T, page: i32, count: usize) -> Self {
        Self {
            success: true,
            page: Some(page),
            count: Some(count),
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// ❌ 创建错误响应（带分页信息）
    pub fn error_with_page(error: String, page: i32) -> Self {
        Self {
            success: false,
            page: Some(page),
            count: Some(0),
            data: None,
            error: Some(error),
            timestamp: Utc::now(),
        }
    }
}

/// 🏠 系统状态信息
#[derive(Debug, Serialize)]
pub struct SystemStatus {
    pub message: String,
    pub status: String,
    pub version: String,
    pub uptime: String,
    pub apis: Vec<ApiInfo>,
    pub timestamp: DateTime<Utc>,
}

/// 🛣️ API信息
#[derive(Debug, Serialize)]
pub struct ApiInfo {
    pub method: String,
    pub path: String,
    pub description: String,
}

impl SystemStatus {
    /// 🚀 创建系统状态
    pub fn new() -> Self {
        Self {
            message: "🚀 XR爬虫系统 (Rust科学级实现)".to_string(),
            status: "running".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            uptime: "刚刚启动".to_string(),
            apis: vec![
                ApiInfo {
                    method: "GET".to_string(),
                    path: "/".to_string(),
                    description: "系统状态检查".to_string(),
                },
                ApiInfo {
                    method: "GET".to_string(),
                    path: "/getlist?page=1".to_string(),
                    description: "获取列表页数据 - 展示Rust极致性能".to_string(),
                },
            ],
            timestamp: Utc::now(),
        }
    }
}
