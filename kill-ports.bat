@echo off
echo ========================================
echo XR Gallery 端口清理脚本
echo ========================================

echo.
echo 正在清理端口占用...

REM 杀掉3331端口进程（前端）
echo [1/2] 清理前端端口 3331...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3331') do (
    echo 杀掉进程 %%a (端口3331)
    taskkill /f /pid %%a >nul 2>&1
)

REM 杀掉3332端口进程（后端）
echo [2/2] 清理后端端口 3332...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3332') do (
    echo 杀掉进程 %%a (端口3332)
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo ========================================
echo 端口清理完成！
echo 3331 (前端) 和 3332 (后端) 端口已释放
echo ========================================
echo.
echo 按任意键退出...
pause >nul
