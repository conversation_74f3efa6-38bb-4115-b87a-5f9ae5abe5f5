/*
 * Copyright 2023-2024 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */
QLOG_EVENT(connectivity, connection_started)
QLOG_EVENT(connectivity, connection_state_updated)
QLOG_EVENT(connectivity, connection_closed)
QLOG_EVENT(transport, parameters_set)
QLOG_EVENT(transport, packet_sent)
QLOG_EVENT(transport, packet_received)
QLOG_EVENT(recovery, packet_lost)
