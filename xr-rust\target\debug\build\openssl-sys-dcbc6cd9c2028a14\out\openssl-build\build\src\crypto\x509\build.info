LIBS=../../libcrypto
SOURCE[../../libcrypto]=\
        x509_def.c x509_d2.c x509_r2x.c x509_cmp.c \
        x509_obj.c x509_req.c x509spki.c x509_vfy.c \
        x509_set.c x509cset.c x509rset.c x509_err.c \
        x509name.c x509_v3.c x509_ext.c x509_att.c \
        x509_meth.c x509_lu.c x_all.c x509_txt.c \
        x509_trust.c by_file.c by_dir.c by_store.c x509_vpm.c \
        x_crl.c t_crl.c x_req.c t_req.c x_x509.c t_x509.c \
        x_pubkey.c x_x509a.c x_attrib.c x_exten.c x_name.c \
        v3_bcons.c v3_bitst.c v3_conf.c v3_extku.c v3_ia5.c v3_utf8.c v3_lib.c \
        v3_prn.c v3_utl.c v3err.c v3_genn.c v3_san.c v3_skid.c v3_akid.c \
        v3_pku.c v3_int.c v3_enum.c v3_sxnet.c v3_cpols.c v3_crld.c v3_purp.c \
        v3_info.c v3_akeya.c v3_pmaps.c v3_pcons.c v3_ncons.c \
        v3_pcia.c v3_pci.c v3_ist.c \
        pcy_cache.c pcy_node.c pcy_data.c pcy_map.c pcy_tree.c pcy_lib.c \
        v3_asid.c v3_addr.c v3_tlsf.c v3_admis.c v3_no_rev_avail.c \
        v3_soa_id.c v3_no_ass.c v3_group_ac.c v3_single_use.c v3_ind_iss.c \
        x509_acert.c x509aset.c t_acert.c x_ietfatt.c v3_ac_tgt.c v3_sda.c \
        v3_usernotice.c v3_battcons.c v3_audit_id.c v3_iobo.c v3_authattid.c \
        v3_rolespec.c v3_attrdesc.c v3_timespec.c v3_attrmap.c v3_aaa.c

IF[{- !$disabled{'deprecated-3.0'} -}]
  SOURCE[../../libcrypto]=x509type.c
ENDIF
