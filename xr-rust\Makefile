# XR-Crawler Makefile
# 跨平台构建配置

.PHONY: help build build-release build-all clean test lint fmt check install-cross

# 默认目标
help:
	@echo "🚀 XR-Crawler 构建系统"
	@echo ""
	@echo "可用命令:"
	@echo "  build         - 本地调试构建"
	@echo "  build-release - 本地发布构建"
	@echo "  build-all     - 全平台发布构建"
	@echo "  build-windows - Windows x64 构建"
	@echo "  build-linux   - Linux x64 构建"
	@echo "  build-arm64   - Linux ARM64 构建"
	@echo "  test          - 运行测试"
	@echo "  lint          - 代码检查"
	@echo "  fmt           - 代码格式化"
	@echo "  check         - 快速检查"
	@echo "  clean         - 清理构建目录"
	@echo "  install-cross - 安装交叉编译工具"

# 本地构建
build:
	@echo "🔧 本地调试构建..."
	cargo build

build-release:
	@echo "📦 本地发布构建..."
	cargo build --release

# 跨平台构建
build-windows:
	@echo "🪟 Windows x64 构建..."
	cargo build --release --target x86_64-pc-windows-msvc

build-linux: install-cross
	@echo "🐧 Linux x64 构建..."
	cross build --release --target x86_64-unknown-linux-gnu

build-arm64: install-cross
	@echo "💪 Linux ARM64 构建..."
	cross build --release --target aarch64-unknown-linux-gnu

build-all: install-cross
	@echo "🌍 全平台构建..."
	@echo "🪟 构建 Windows x64..."
	cargo build --release --target x86_64-pc-windows-msvc
	@echo "🐧 构建 Linux x64..."
	cross build --release --target x86_64-unknown-linux-gnu
	@echo "💪 构建 Linux ARM64..."
	cross build --release --target aarch64-unknown-linux-gnu
	@echo "✅ 全平台构建完成!"

# 开发工具
test:
	@echo "🧪 运行测试..."
	cargo test --all-features

lint:
	@echo "🔍 代码检查..."
	cargo clippy --all-targets --all-features -- -D warnings

fmt:
	@echo "🎨 代码格式化..."
	cargo fmt --all

check:
	@echo "⚡ 快速检查..."
	cargo check --all-targets --all-features

# 清理
clean:
	@echo "🧹 清理构建目录..."
	cargo clean

# 工具安装
install-cross:
	@echo "📦 检查 cross 工具..."
	@command -v cross >/dev/null 2>&1 || { \
		echo "安装 cross 工具..."; \
		cargo install cross --git https://github.com/cross-rs/cross; \
	}

# 安装目标
install-targets:
	@echo "📦 安装编译目标..."
	rustup target add x86_64-pc-windows-msvc
	rustup target add x86_64-unknown-linux-gnu
	rustup target add aarch64-unknown-linux-gnu

# CI/CD 相关
ci-test: fmt lint test check

ci-build: build-all

# 发布准备
prepare-release: clean ci-test build-all
	@echo "🎉 发布准备完成!"
