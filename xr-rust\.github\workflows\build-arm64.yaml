name: Build for ARM64 (musl)

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build the Docker image
      run: docker build -t xr-crawler-arm64 -f Dockerfile.arm64 .

    - name: Extract the binary from the Docker image
      run: |
        docker create --name extractor xr-crawler-arm64
        docker cp extractor:/app/target/aarch64-unknown-linux-musl/release/xr-crawler ./xr-crawler-arm64-musl
        docker rm extractor

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: xr-crawler-arm64-musl
        path: xr-crawler-arm64-musl
