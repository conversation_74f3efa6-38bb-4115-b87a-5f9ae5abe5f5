# XR-Crawler 构建脚本 (PowerShell)
# 支持本地和跨平台构建

param(
    [string]$Target = "native",
    [switch]$Release = $false,
    [switch]$Clean = $false
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 XR-Crawler 构建脚本" -ForegroundColor Green
Write-Host "目标平台: $Target" -ForegroundColor Yellow

# 清理构建目录
if ($Clean) {
    Write-Host "🧹 清理构建目录..." -ForegroundColor Blue
    if (Test-Path "target") {
        Remove-Item -Recurse -Force "target"
    }
}

# 设置构建参数
$BuildArgs = @()
if ($Release) {
    $BuildArgs += "--release"
    Write-Host "📦 发布模式构建" -ForegroundColor Green
} else {
    Write-Host "🔧 调试模式构建" -ForegroundColor Yellow
}

# 根据目标平台选择构建方式
switch ($Target.ToLower()) {
    "native" {
        Write-Host "🏠 本地平台构建..." -ForegroundColor Blue
        cargo build @BuildArgs
    }
    "windows" {
        Write-Host "🪟 Windows x64 构建..." -ForegroundColor Blue
        $BuildArgs += "--target", "x86_64-pc-windows-msvc"
        cargo build @BuildArgs
    }
    "linux" {
        Write-Host "🐧 Linux x64 构建..." -ForegroundColor Blue
        $BuildArgs += "--target", "x86_64-unknown-linux-gnu"
        
        # 检查是否安装了 cross
        if (!(Get-Command "cross" -ErrorAction SilentlyContinue)) {
            Write-Host "📦 安装 cross 工具..." -ForegroundColor Yellow
            cargo install cross --git https://github.com/cross-rs/cross
        }
        
        cross build @BuildArgs
    }
    "arm64" {
        Write-Host "💪 Linux ARM64 构建..." -ForegroundColor Blue
        $BuildArgs += "--target", "aarch64-unknown-linux-gnu"
        
        # 检查是否安装了 cross
        if (!(Get-Command "cross" -ErrorAction SilentlyContinue)) {
            Write-Host "📦 安装 cross 工具..." -ForegroundColor Yellow
            cargo install cross --git https://github.com/cross-rs/cross
        }
        
        cross build @BuildArgs
    }
    "all" {
        Write-Host "🌍 全平台构建..." -ForegroundColor Blue
        
        # 检查是否安装了 cross
        if (!(Get-Command "cross" -ErrorAction SilentlyContinue)) {
            Write-Host "📦 安装 cross 工具..." -ForegroundColor Yellow
            cargo install cross --git https://github.com/cross-rs/cross
        }
        
        # Windows
        Write-Host "🪟 构建 Windows x64..." -ForegroundColor Cyan
        cargo build @BuildArgs --target x86_64-pc-windows-msvc
        
        # Linux x64
        Write-Host "🐧 构建 Linux x64..." -ForegroundColor Cyan
        cross build @BuildArgs --target x86_64-unknown-linux-gnu
        
        # Linux ARM64
        Write-Host "💪 构建 Linux ARM64..." -ForegroundColor Cyan
        cross build @BuildArgs --target aarch64-unknown-linux-gnu
    }
    default {
        Write-Error "❌ 不支持的目标平台: $Target"
        Write-Host "支持的平台: native, windows, linux, arm64, all" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "✅ 构建完成!" -ForegroundColor Green

# 显示构建产物
if ($Release) {
    $BuildDir = "target/release"
} else {
    $BuildDir = "target/debug"
}

if (Test-Path $BuildDir) {
    Write-Host "📁 构建产物:" -ForegroundColor Blue
    Get-ChildItem $BuildDir -Name "xr-crawler*" | ForEach-Object {
        Write-Host "  - $_" -ForegroundColor Gray
    }
}
