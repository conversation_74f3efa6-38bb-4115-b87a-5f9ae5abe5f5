{"rustc": 3926191382657067107, "features": "[\"default\"]", "declared_features": "[\"default\", \"has_bytes\", \"log\", \"nats\", \"nats_storage\", \"postgres-native-tls\", \"postgres-openssl\", \"postgres_native_tls\", \"postgres_openssl\", \"postgres_storage\", \"prost\", \"prost-build\", \"signal\", \"tokio-postgres\", \"tracing-subscriber\"]", "target": 2318398449392625286, "profile": 11876527447619405325, "path": 11178157185526922528, "deps": [[5157631553186200874, "num_traits", false, 3155075673551980203], [5990956534088275425, "num_derive", false, 4708121534806728210], [7294361402007043243, "cron", false, 10534808174367258280], [8319709847752024821, "uuid", false, 14312247276815470363], [8606274917505247608, "tracing", false, 7681878162268537011], [9897246384292347999, "chrono", false, 12539715333476479893], [12555647011536806788, "build_script_build", false, 4294263361015852610], [12944427623413450645, "tokio", false, 15696514691787130065]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-cron-scheduler-1422fe344395b76b\\dep-lib-tokio_cron_scheduler", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}