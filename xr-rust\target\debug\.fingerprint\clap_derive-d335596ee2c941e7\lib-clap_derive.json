{"rustc": 3926191382657067107, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 5896785871467616221, "path": 3024034207374266352, "deps": [[3060637413840920116, "proc_macro2", false, 2141545387885295045], [4974441333307933176, "syn", false, 13480060149965268283], [13077543566650298139, "heck", false, 15159348294400826071], [17990358020177143287, "quote", false, 8737441731687220448]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_derive-d335596ee2c941e7\\dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}