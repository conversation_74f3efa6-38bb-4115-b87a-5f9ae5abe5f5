{"rustc": 3926191382657067107, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 11876527447619405325, "path": 6933343522593354184, "deps": [[2828590642173593838, "cfg_if", false, 12354336994155717562]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\encoding_rs-b03785496e2fe6e6\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}