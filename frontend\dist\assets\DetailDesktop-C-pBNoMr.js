import{r as E,c as L,l as B,i as j,m as S,k as A,p as F,a as l,b as n,f as c,d as u,w as d,u as e,B as q,t as i,N as g,j as M,F as K,e as U,q as T,s as H,o,h as m,n as J,v as $}from"./index-BpqYk2C3.js";import{_ as O,a as P,u as Q,L as W,E as X}from"./ErrorMessage-CVNw5bDd.js";import{a as Y}from"./device-Bim2jCTP.js";const Z={class:"detail-desktop"},ee={class:"desktop-header"},te={class:"container"},ae={key:0,class:"header-info"},ne={class:"gallery-title"},re={class:"gallery-meta"},se={class:"desktop-content"},oe={class:"container"},le={key:2,class:"image-viewer"},ie={class:"main-image-area"},ce={class:"main-image-container"},ue=["src","alt"],de={class:"image-info"},ve={class:"image-counter"},me={class:"thumbnail-strip"},_e=["onClick"],ge=["src","alt"],he={class:"thumbnail-number"},ye={key:3,class:"desktop-navigation"},pe={key:0,class:"nav-item"},fe={class:"nav-content"},ke={class:"nav-info"},be={key:1,class:"nav-item"},Ge={class:"nav-content"},xe={class:"nav-info"},De={__name:"DetailDesktop",setup(Ie){const _=S(),h=A(),y=P(),a=Q(),s=E(0),p=E(null),V=L(()=>{var r;const t=(r=a.currentGallery)==null?void 0:r.navigation;return t&&(t.prev||t.next)}),b=L(()=>{var t;return(t=a.currentGallery)==null?void 0:t.images[s.value]});async function f(){const t=parseInt(_.params.xrid);if(!t){y.error("无效的图库ID");return}try{await a.fetchGalleryDetail(t),s.value=0}catch{y.error("加载详情失败")}}function G(){try{h.push("/")}catch(t){console.error("路由跳转失败:",t),y.error("返回首页失败")}}function x(t){h.push({name:"DetailDesktop",params:{xrid:t}})}function k(t){s.value=t,w(t)}function D(){s.value>0&&k(s.value-1)}function I(){s.value<a.currentGallery.images.length-1&&k(s.value+1)}function w(t){H(()=>{if(p.value){const r=p.value.children;r[t]&&r[t].scrollIntoView({behavior:"smooth",block:"nearest",inline:"center"})}})}function C(t){switch(t.key){case"ArrowLeft":t.preventDefault(),D();break;case"ArrowRight":t.preventDefault(),I();break;case"Escape":t.preventDefault(),G();break}}function z(){console.log("主图加载成功")}function R(){console.error("主图加载失败")}return B(()=>_.params.xrid,()=>{_.name==="DetailDesktop"&&f()}),B(s,t=>{w(t)}),j(()=>{if(Y()){const t=_.params.xrid;console.log(`检测到移动设备，从桌面端详情页跳转到移动端详情页: ${t}`),h.replace({name:"DetailMobile",params:{xrid:t}});return}f(),document.addEventListener("keydown",C)}),F(()=>{document.removeEventListener("keydown",C)}),(t,r)=>(o(),l("div",Z,[n("header",ee,[n("div",te,[c(e(q),{onClick:G,type:"primary",ghost:""},{default:d(()=>r[2]||(r[2]=[m(" ← 返回首页 ",-1)])),_:1,__:[2]}),e(a).currentGallery?(o(),l("div",ae,[n("h1",ne,i(e(a).currentGallery.info.title),1),n("div",re,[c(e(g),{type:"info"},{default:d(()=>[m("ID: "+i(e(a).currentGallery.info.xrid),1)]),_:1}),c(e(g),{type:"success"},{default:d(()=>[m(i(e(a).currentGallery.images.length)+" 张图片",1)]),_:1})])])):u("",!0)])]),n("main",se,[n("div",oe,[e(a).loading?(o(),M(W,{key:0,text:"加载详情中..."})):e(a).error?(o(),M(X,{key:1,title:e(a).error,description:"无法加载图片详情","show-back":"",onRetry:f},null,8,["title"])):e(a).currentGallery?(o(),l("div",le,[n("div",ie,[n("div",ce,[n("img",{src:b.value.reurl,alt:`图片 ${b.value.order}`,class:"main-image",onLoad:z,onError:R},null,40,ue),n("div",de,[n("span",ve,i(s.value+1)+" / "+i(e(a).currentGallery.images.length),1)])]),s.value>0?(o(),l("button",{key:0,onClick:D,class:"nav-button nav-button-prev"}," ‹ ")):u("",!0),s.value<e(a).currentGallery.images.length-1?(o(),l("button",{key:1,onClick:I,class:"nav-button nav-button-next"}," › ")):u("",!0)]),n("div",me,[n("div",{class:"thumbnail-container",ref_key:"thumbnailContainer",ref:p},[(o(!0),l(K,null,U(e(a).currentGallery.images,(v,N)=>(o(),l("div",{key:v.id,class:J(["thumbnail-item",{active:N===s.value}]),onClick:we=>k(N)},[n("img",{src:v.reurl,alt:`缩略图 ${v.order}`,class:"thumbnail-image",loading:"lazy"},null,8,ge),n("div",he,i(v.order),1)],10,_e))),128))],512)])])):u("",!0),V.value?(o(),l("div",ye,[e(a).currentGallery.navigation.prev?(o(),l("div",pe,[r[3]||(r[3]=n("h3",null,"上一套",-1)),c(e(T),{class:"nav-card",hoverable:"",onClick:r[0]||(r[0]=v=>x(e(a).currentGallery.navigation.prev.xrid))},{default:d(()=>[n("div",fe,[c(e($),{src:e(a).currentGallery.navigation.prev.cover,alt:e(a).currentGallery.navigation.prev.title,"object-fit":"cover","preview-disabled":"",class:"nav-image"},null,8,["src","alt"]),n("div",ke,[n("h4",null,i(e(a).currentGallery.navigation.prev.title),1),c(e(g),{size:"small"},{default:d(()=>[m("ID: "+i(e(a).currentGallery.navigation.prev.xrid),1)]),_:1})])])]),_:1})])):u("",!0),e(a).currentGallery.navigation.next?(o(),l("div",be,[r[4]||(r[4]=n("h3",null,"下一套",-1)),c(e(T),{class:"nav-card",hoverable:"",onClick:r[1]||(r[1]=v=>x(e(a).currentGallery.navigation.next.xrid))},{default:d(()=>[n("div",Ge,[c(e($),{src:e(a).currentGallery.navigation.next.cover,alt:e(a).currentGallery.navigation.next.title,"object-fit":"cover","preview-disabled":"",class:"nav-image"},null,8,["src","alt"]),n("div",xe,[n("h4",null,i(e(a).currentGallery.navigation.next.title),1),c(e(g),{size:"small"},{default:d(()=>[m("ID: "+i(e(a).currentGallery.navigation.next.xrid),1)]),_:1})])])]),_:1})])):u("",!0)])):u("",!0)])])]))}},Le=O(De,[["__scopeId","data-v-b81d40d7"]]);export{Le as default};
