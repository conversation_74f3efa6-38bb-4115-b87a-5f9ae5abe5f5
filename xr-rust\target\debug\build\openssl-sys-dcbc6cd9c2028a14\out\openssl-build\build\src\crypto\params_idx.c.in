/*
 * {- join("\n * ", @autowarntext) -}
 *
 * Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */
{-
use OpenSSL::paramnames qw(produce_decoder);
-}

#include "internal/e_os.h"
#include "internal/param_names.h"
#include <string.h>

/* Machine generated TRIE -- generated by util/perl/OpenSSL/paramnames.pm */
{- produce_decoder(); -}
/* End of TRIE */
