use anyhow::{anyhow, Result};
use reqwest::{multipart, Client};
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn};

/// 🚀 上传服务 - Rust科学级图片上传实现
///
/// 功能特性：
/// - 🔄 智能重试机制
/// - 🗄️ 上传结果缓存
/// - 🔍 图片指纹去重
/// - ⚡ 异步高性能处理
/// - 🛡️ 内存安全保证
pub struct UploadService {
    client: Client,
    upload_url: String,
    cache: Arc<RwLock<HashMap<String, CachedUpload>>>,
}

/// 📦 上传结果结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadResult {
    pub src: String,
}

/// 🗄️ 缓存的上传结果
#[derive(Debug, <PERSON><PERSON>)]
struct CachedUpload {
    pub reurl: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl UploadService {
    /// 🏗️ 创建新的上传服务实例
    pub fn new() -> Self {
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(60))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            upload_url: "https://img1.101616.xyz/upload".to_string(),
            cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 🖼️ 上传图片到Telegraph
    ///
    /// 智能上传流程：
    /// 1. 下载原始图片
    /// 2. 生成图片指纹
    /// 3. 检查缓存避免重复上传
    /// 4. 上传到Telegraph服务器
    /// 5. 缓存上传结果
    pub async fn upload_image(&self, image_url: &str) -> Result<Vec<UploadResult>> {
        info!("🚀 开始上传图片: {}", image_url);

        // 1. 下载图片数据
        let image_data = self.download_image(image_url).await?;

        // 2. 生成图片指纹
        let image_hash = self.generate_image_hash(&image_data);
        info!("🔍 图片指纹: {}", image_hash);

        // 3. 检查缓存
        if let Some(cached_result) = self.get_cached_result(&image_hash).await {
            info!("♻️  使用缓存结果: {} -> {}", image_url, cached_result);
            return Ok(vec![UploadResult { src: cached_result }]);
        }

        // 4. 上传到Telegraph
        let result = self.upload_to_telegraph(&image_data).await?;

        // 5. 缓存成功的上传结果
        if result.src.starts_with("/file/") {
            self.cache_upload_result(&image_hash, &result.src).await;
        }

        info!("🎉 上传成功: {} -> {}", image_url, result.src);
        Ok(vec![result])
    }

    /// 🌐 构建完整图片URL
    ///
    /// 处理各种URL格式：
    /// - 相对路径 -> 完整URL
    /// - 已有协议 -> 直接使用
    /// - 代理URL处理
    pub async fn build_image_url(&self, relative_url: &str) -> Result<String> {
        // 如果已经是完整URL，直接返回
        if relative_url.starts_with("http://") || relative_url.starts_with("https://") {
            return Ok(relative_url.to_string());
        }

        // 读取基础URL配置
        let base_url = self.get_base_url().await?;
        let proxy_prefix = "https://re.101616.xyz/";

        // 构建完整URL
        let full_url = if relative_url.starts_with("/") {
            format!("{}{}{}", proxy_prefix, base_url, relative_url)
        } else {
            format!("{}{}/{}", proxy_prefix, base_url, relative_url)
        };

        Ok(full_url)
    }

    /// 📥 下载图片数据
    async fn download_image(&self, image_url: &str) -> Result<Vec<u8>> {
        let response = self.client
            .get(image_url)
            .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow!("下载图片失败: HTTP {}", response.status()));
        }

        let image_data = response.bytes().await?;

        if image_data.is_empty() {
            return Err(anyhow!("下载的图片数据为空"));
        }

        info!("📥 图片下载成功: {} bytes", image_data.len());
        Ok(image_data.to_vec())
    }

    /// 🔍 生成图片指纹
    fn generate_image_hash(&self, image_data: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(image_data);
        format!("{:x}", hasher.finalize())
    }

    /// 🗄️ 获取缓存结果
    async fn get_cached_result(&self, image_hash: &str) -> Option<String> {
        let cache = self.cache.read().await;
        if let Some(cached) = cache.get(image_hash) {
            // 检查缓存是否过期（1小时）
            let now = chrono::Utc::now();
            if now.signed_duration_since(cached.timestamp).num_seconds() < 3600 {
                return Some(cached.reurl.clone());
            }
        }
        None
    }

    /// 💾 缓存上传结果
    async fn cache_upload_result(&self, image_hash: &str, reurl: &str) {
        let mut cache = self.cache.write().await;
        cache.insert(image_hash.to_string(), CachedUpload {
            reurl: reurl.to_string(),
            timestamp: chrono::Utc::now(),
        });
    }

    /// 🚀 上传到Telegraph服务器
    async fn upload_to_telegraph(&self, image_data: &[u8]) -> Result<UploadResult> {
        // 创建multipart表单
        let form = multipart::Form::new()
            .part("file", multipart::Part::bytes(image_data.to_vec())
                .file_name("image.jpg")
                .mime_str("image/jpeg")?);

        // 发送上传请求
        let response = self.client
            .post(&self.upload_url)
            .multipart(form)
            .send()
            .await?;

        if !response.status().is_success() {
            warn!("❌ 上传HTTP状态码错误: {}", response.status());
            return Ok(UploadResult { src: "4040".to_string() });
        }

        // 解析响应
        let response_text = response.text().await?;

        // 简单解析Telegraph响应格式
        if let Some(src) = self.extract_src_from_response(&response_text) {
            Ok(UploadResult { src })
        } else {
            warn!("❌ 无法解析上传响应: {}", response_text);
            Ok(UploadResult { src: "4040".to_string() })
        }
    }

    /// 🔍 从响应中提取src字段
    fn extract_src_from_response(&self, response: &str) -> Option<String> {
        // 查找 "src": 字段
        if let Some(start_pos) = response.find(r#""src":""#) {
            let start = start_pos + 7; // 跳过 "src":"
            if let Some(end_pos) = response[start..].find('"') {
                let src = &response[start..start + end_pos];
                return Some(src.to_string());
            }
        }
        None
    }

    /// 🌐 获取基础URL
    async fn get_base_url(&self) -> Result<String> {
        // 尝试从文件读取
        if let Ok(content) = tokio::fs::read_to_string("finalUrl.txt").await {
            let url = content.trim();
            if !url.is_empty() {
                return Ok(url.to_string());
            }
        }

        // 默认URL
        Ok("https://www.xiu01.top/".to_string())
    }
}

impl Default for UploadService {
    fn default() -> Self {
        Self::new()
    }
}
