/// 简单的编码修复测试
fn main() {
    println!("开始测试中文编码修复功能");

    // 测试用例 - 使用字节数组避免编码问题
    let test_case_bytes = b"[Xiu<PERSON>en\xc3\xa7\xc2\xa7\xc2\x80\xc3\xa4\xc2\xba\xc2\xba\xc3\xa7\xc2\xbd\xc2\x91]No.10550";
    let test_case = String::from_utf8_lossy(test_case_bytes);

    println!("原始: {}", test_case);

    let fixed = fix_chinese_encoding(&test_case);
    println!("修复: {}", fixed);

    if fixed != test_case {
        println!("编码已修复");
    } else {
        println!("无需修复或修复失败");
    }
}

/// 检测并修复中文编码问题
fn fix_chinese_encoding(input: &str) -> String {
    if input.is_empty() {
        return input.to_string();
    }

    // 检测是否包含双重编码特征 - 使用字节模式
    let has_encoding_issue = input.as_bytes().windows(3).any(|window| {
        matches!(window,
            [0xc3, 0xa7, 0xc2] | // ç§
            [0xc3, 0xa4, 0xc2] | // ä
            [0xc3, 0xa6, 0xc2]   // æ
        )
    });

    if !has_encoding_issue {
        return input.to_string();
    }

    // 尝试Latin1修复
    let bytes: Vec<u8> = input.chars()
        .filter_map(|c| {
            let code = c as u32;
            if code <= 255 {
                Some(code as u8)
            } else {
                None
            }
        })
        .collect();

    if let Ok(result) = String::from_utf8(bytes) {
        if result != input {
            let fixed_chinese_count = result.chars()
                .filter(|&c| {
                    let code = c as u32;
                    (0x4E00..=0x9FFF).contains(&code)
                })
                .count();
            
            let original_chinese_count = input.chars()
                .filter(|&c| {
                    let code = c as u32;
                    (0x4E00..=0x9FFF).contains(&code)
                })
                .count();

            if fixed_chinese_count > original_chinese_count && fixed_chinese_count > 3 {
                return result;
            }
        }
    }

    input.to_string()
}
