{"rustc": 3926191382657067107, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 18350989681977977651, "path": 3380744381593841601, "deps": [[1457576002496728321, "clap_derive", false, 2469075813672418114], [7361794428713524931, "clap_builder", false, 1268190725668622420]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-55fd77603553df9f\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}