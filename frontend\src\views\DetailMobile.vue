<template>
  <div class="detail-mobile">
    <!-- 移动端头部 -->
    <header class="mobile-header">
      <div class="header-content">
        <button @click="goBack" class="back-button">
          ← 返回
        </button>
        <div class="header-info" v-if="galleryStore.currentGallery">
          <h1 class="gallery-title">{{ galleryStore.currentGallery.info.title }}</h1>
          <div class="gallery-meta">
            <span class="image-count">{{ galleryStore.currentGallery.images.length }} 张图片</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="mobile-content">
      <!-- 加载状态 -->
      <Loading v-if="galleryStore.loading" text="加载中..." />

      <!-- 错误状态 -->
      <ErrorMessage
        v-else-if="galleryStore.error"
        :title="galleryStore.error"
        description="无法加载图片"
        show-back
        @retry="loadDetail"
      />

      <!-- 瀑布流图片 -->
      <div v-else-if="galleryStore.currentGallery" class="waterfall-container">
        <div
          v-for="(image, index) in galleryStore.currentGallery.images"
          :key="image.id"
          class="waterfall-item"
        >
          <img
            :src="image.reurl"
            :alt="`图片 ${image.order}`"
            class="waterfall-image"
            loading="lazy"
            @load="onImageLoad"
            @error="onImageError"
          />
          <div class="image-number">{{ image.order }}</div>
        </div>

        <!-- 导航区域 -->
        <div class="mobile-navigation" v-if="hasNavigation">
          <!-- 上一套 -->
          <div class="nav-item" v-if="galleryStore.currentGallery.navigation.prev">
            <h3>上一套</h3>
            <div
              class="nav-card"
              @click="goToGallery(galleryStore.currentGallery.navigation.prev.xrid)"
            >
              <img
                :src="galleryStore.currentGallery.navigation.prev.cover"
                :alt="galleryStore.currentGallery.navigation.prev.title"
                class="nav-image"
              />
              <div class="nav-info">
                <h4>{{ galleryStore.currentGallery.navigation.prev.title }}</h4>
                <span class="nav-id">ID: {{ galleryStore.currentGallery.navigation.prev.xrid }}</span>
              </div>
            </div>
          </div>

          <!-- 下一套 -->
          <div class="nav-item" v-if="galleryStore.currentGallery.navigation.next">
            <h3>下一套</h3>
            <div
              class="nav-card"
              @click="goToGallery(galleryStore.currentGallery.navigation.next.xrid)"
            >
              <img
                :src="galleryStore.currentGallery.navigation.next.cover"
                :alt="galleryStore.currentGallery.navigation.next.title"
                class="nav-image"
              />
              <div class="nav-info">
                <h4>{{ galleryStore.currentGallery.navigation.next.title }}</h4>
                <span class="nav-id">ID: {{ galleryStore.currentGallery.navigation.next.xrid }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useGalleryStore } from '@/stores/gallery'
import { isDesktopDevice } from '@/utils/device'
import Loading from '@/components/Loading.vue'
import ErrorMessage from '@/components/ErrorMessage.vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const galleryStore = useGalleryStore()

// 计算属性
const hasNavigation = computed(() => {
  const nav = galleryStore.currentGallery?.navigation
  return nav && (nav.prev || nav.next)
})

// 加载详情
async function loadDetail() {
  const xrid = parseInt(route.params.xrid)
  if (!xrid) {
    message.error('无效的图库ID')
    return
  }

  try {
    await galleryStore.fetchGalleryDetail(xrid)
  } catch (error) {
    message.error('加载详情失败')
  }
}

// 返回首页
function goBack() {
  try {
    router.push('/')
  } catch (error) {
    console.error('路由跳转失败:', error)
    message.error('返回首页失败')
  }
}

// 跳转到其他图库
function goToGallery(xrid) {
  router.push({ name: 'DetailMobile', params: { xrid } })
}

// 图片加载成功
function onImageLoad(event) {
  console.log('图片加载成功:', event.target.src)
}

// 图片加载失败
function onImageError(event) {
  console.error('图片加载失败:', event.target.src)
}

// 监听路由变化
watch(() => route.params.xrid, () => {
  if (route.name === 'DetailMobile') {
    loadDetail()
  }
})

// 组件挂载时加载数据
// 组件挂载时加载数据并禁止页面滑动
onMounted(() => {
  // 检测设备类型，如果是桌面设备则跳转到桌面端详情页
  if (isDesktopDevice()) {
    const xrid = route.params.xrid
    console.log(`检测到桌面设备，从移动端详情页跳转到桌面端详情页: ${xrid}`)
    router.replace({ name: 'DetailDesktop', params: { xrid } })
    return
  }

  loadDetail()

  // 禁止页面左右滑动
  document.body.style.overflowX = 'hidden'
  document.documentElement.style.overflowX = 'hidden'

  // 禁止触摸滑动
  const preventHorizontalScroll = (e) => {
    if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
      e.preventDefault()
    }
  }

  document.addEventListener('wheel', preventHorizontalScroll, { passive: false })
  document.addEventListener('touchmove', (e) => {
    // 只允许垂直滚动
    if (e.touches.length === 1) {
      const touch = e.touches[0]
      const startY = touch.clientY

      // 如果是水平滑动，阻止默认行为
      if (Math.abs(touch.clientX - (touch.startX || touch.clientX)) >
          Math.abs(touch.clientY - (touch.startY || touch.clientY))) {
        e.preventDefault()
      }
    }
  }, { passive: false })
})

// 组件卸载时恢复页面滑动
onUnmounted(() => {
  document.body.style.overflowX = ''
  document.documentElement.style.overflowX = ''
})
</script>

<style scoped>
/* 全局禁止水平滚动 */
:global(html, body) {
  overflow-x: hidden !important;
  max-width: 100vw !important;
  width: 100vw !important;
}

:global(*) {
  max-width: 100vw !important;
}</style>

<style scoped>
/* 移动端详情页样式 */
.detail-mobile {
  min-height: 100vh;
  background: var(--bg-primary);
  overflow-x: hidden; /* 禁止水平滚动 */
  width: 100vw;
  max-width: 100vw;
}

/* 移动端头部 */
.mobile-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-sm) 0;
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

.header-content {
  padding: 0 var(--spacing-md);
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  overflow-x: hidden;
}

.back-button {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  margin-bottom: var(--spacing-sm);
}

.back-button:hover {
  background: var(--bg-secondary);
}

.gallery-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.4;
}

.gallery-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.image-count {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* 主要内容 */
.mobile-content {
  padding: 0;
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
}

/* 瀑布流容器 */
.waterfall-container {
  padding: 0;
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
}

.waterfall-item {
  position: relative;
  margin-bottom: 0;
  width: 100vw;
  max-width: 100vw;
}

.waterfall-image {
  width: 100vw;
  max-width: 100vw;
  height: auto;
  display: block;
  object-fit: contain;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

.image-number {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* 移动端导航 */
.mobile-navigation {
  padding: var(--spacing-lg) var(--spacing-md);
  background: var(--bg-secondary);
  margin-top: var(--spacing-lg);
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

.nav-item {
  margin-bottom: var(--spacing-lg);
}

.nav-item:last-child {
  margin-bottom: 0;
}

.nav-item h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.nav-card {
  display: flex;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.nav-card:active {
  transform: scale(0.98);
}

.nav-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  flex-shrink: 0;
}

.nav-info {
  padding: var(--spacing-sm);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.nav-info h4 {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.nav-id {
  font-size: 0.8rem;
  color: var(--text-tertiary);
}
</style>
