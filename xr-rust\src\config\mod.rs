use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::env;
use std::fs;
use tracing::{info, warn};

/// 🔧 科学级配置管理系统
/// 
/// 特性：
/// - 🎯 类型安全的配置定义
/// - 🌍 环境变量优先级处理
/// - 📁 文件配置自动加载
/// - 🔄 动态URL更新机制
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub crawler: CrawlerConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub port: u16,
    pub host: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrawlerConfig {
    pub base_url: String,
    pub proxy_prefix: String,
    pub user_agent: String,
    pub timeout: u64,
}

impl Config {
    /// 🚀 智能配置加载系统
    /// 
    /// 加载优先级：
    /// 1. 环境变量 (最高优先级)
    /// 2. .env 文件
    /// 3. 默认值 (兜底保证)
    pub async fn load(port: Option<u16>) -> Result<Self> {
        // 🔍 尝试加载 .env 文件
        if let Err(_) = dotenvy::dotenv() {
            warn!("⚠️  未找到 .env 文件，使用默认配置");
        } else {
            info!("✅ 成功加载 .env 文件");
        }

        // 🌐 动态获取基础URL
        let base_url = Self::get_dynamic_base_url().await;
        
        let mut config = Config {
            server: ServerConfig {
                port: get_env_var("PORT")
                    .unwrap_or_else(|| "2002".to_string())
                    .parse()
                    .unwrap_or(2002),
                host: get_env_var("HOST").unwrap_or_else(|| "0.0.0.0".to_string()),
            },
            database: DatabaseConfig {
                url: get_env_var("DATABASE_URL")
                    .unwrap_or_else(|| "mysql://root:wangcong@*************:11436/zz?charset=utf8mb4&collation=utf8mb4_unicode_ci".to_string()),
            },
            crawler: CrawlerConfig {
                base_url,
                proxy_prefix: get_env_var("PROXY_PREFIX")
                    .unwrap_or_else(|| "https://re.101616.xyz/".to_string()),
                user_agent: get_env_var("USER_AGENT").unwrap_or_else(|| {
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36".to_string()
                }),
                timeout: get_env_var("TIMEOUT")
                    .unwrap_or_else(|| "30".to_string())
                    .parse()
                    .unwrap_or(30),
            },
        };

        if let Some(p) = port {
            config.server.port = p;
        }

        info!("🎯 配置加载完成:");
        info!("   服务器: {}:{}", config.server.host, config.server.port);
        info!("   数据库: {}", Self::mask_password(&config.database.url));
        info!("   基础URL: {}", config.crawler.base_url);
        info!("   代理前缀: {}", config.crawler.proxy_prefix);

        Ok(config)
    }

    /// 🔄 动态获取基础URL
    /// 
    /// 智能策略：
    /// 1. 优先读取 finalUrl.txt 文件
    /// 2. 文件不存在时使用环境变量
    /// 3. 最后使用硬编码默认值
    async fn get_dynamic_base_url() -> String {
        // 🗂️ 尝试从文件读取
        if let Ok(content) = fs::read_to_string("finalUrl.txt") {
            let url = content.trim();
            if !url.is_empty() {
                info!("📁 从 finalUrl.txt 读取基础URL: {}", url);
                return url.to_string();
            }
        }

        // 🌍 尝试从环境变量读取
        if let Some(url) = get_env_var("BASE_URL") {
            info!("🌍 从环境变量读取基础URL: {}", url);
            return url;
        }

        // 🔧 使用默认值
        let default_url = "https://www.xiu01.top/";
        warn!("⚠️  使用默认基础URL: {}", default_url);
        default_url.to_string()
    }

    /// 🔐 密码掩码处理
    fn mask_password(url: &str) -> String {
        if let Some(at_pos) = url.find('@') {
            if let Some(colon_pos) = url[..at_pos].rfind(':') {
                let mut masked = url.to_string();
                masked.replace_range(colon_pos + 1..at_pos, "****");
                return masked;
            }
        }
        url.to_string()
    }

    /// 🌐 构建代理URL
    /// 
    /// 智能URL构建：
    /// - 自动处理协议前缀
    /// - 避免双重协议问题
    /// - 确保URL格式正确
    pub fn build_proxy_url(&self, path: &str) -> String {
        let clean_base_url = self.crawler.base_url
            .strip_prefix("http://")
            .or_else(|| self.crawler.base_url.strip_prefix("https://"))
            .unwrap_or(&self.crawler.base_url);

        format!("{}{}{}", self.crawler.proxy_prefix, clean_base_url, path)
    }

    /// 🖼️ 构建图片URL
    pub fn build_image_url(&self, image_path: &str) -> String {
        if image_path.starts_with("http://") || image_path.starts_with("https://") {
            return image_path.to_string();
        }

        let clean_base_url = self.crawler.base_url
            .strip_prefix("http://")
            .or_else(|| self.crawler.base_url.strip_prefix("https://"))
            .unwrap_or(&self.crawler.base_url)
            .trim_end_matches('/');

        let clean_path = if image_path.starts_with('/') {
            image_path
        } else {
            &format!("/{}", image_path)
        };

        format!("{}{}{}", self.crawler.proxy_prefix, clean_base_url, clean_path)
    }
}

/// 🔍 智能环境变量获取
fn get_env_var(key: &str) -> Option<String> {
    env::var(key).ok().filter(|s| !s.is_empty())
}
