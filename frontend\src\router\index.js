import { createRouter, createWebHistory } from 'vue-router'
import { getDetailRouteName } from '@/utils/device'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: 'XR Gallery - 首页'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/detail/:xrid',
    name: 'Detail',
    component: () => import('@/views/Detail.vue'),
    meta: {
      title: '图片详情'
    },
    props: true
  },
  {
    path: '/mobile/detail/:xrid',
    name: 'DetailMobile',
    component: () => import('@/views/DetailMobile.vue'),
    meta: {
      title: '图片详情 - 移动端'
    },
    props: true
  },
  {
    path: '/desktop/detail/:xrid',
    name: 'DetailDesktop',
    component: () => import('@/views/DetailDesktop.vue'),
    meta: {
      title: '图片详情 - 桌面端'
    },
    props: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
