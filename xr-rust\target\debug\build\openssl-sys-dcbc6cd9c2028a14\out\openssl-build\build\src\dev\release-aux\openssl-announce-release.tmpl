
   OpenSSL version $release released
   =================================

   OpenSSL - The Open Source toolkit for SSL/TLS
   https://www.openssl.org/

   The OpenSSL project team is pleased to announce the release of
   version $release of our open source toolkit for SSL/TLS.
   For details of the changes, see the release notes at:

        https://www.openssl.org/news/openssl-$series-notes.html

   Specific notes on upgrading to OpenSSL $series from previous versions are
   available in the OpenSSL Migration Guide, here:

        https://www.openssl.org/docs/man$series/man7/ossl-guide-migration.html

   OpenSSL $release is available for download via HTTPS and FTP from the
   following master locations (you can find the various FTP mirrors under
   https://www.openssl.org/source/mirror.html):

     * https://www.openssl.org/source/
     * ftp://ftp.openssl.org/source/

   The distribution file name is:

    o $tarfile
      Size: $length
      SHA1 checksum: $sha1hash
      SHA256 checksum: $sha256hash

   The checksums were calculated using the following commands:

    openssl sha1 $tarfile
    openssl sha256 $tarfile

   Yours,

   The OpenSSL Project Team.

