LIBS=../../libcrypto

$WPASM=wp_block.c
IF[{- !$disabled{asm} -}]
  IF[{- $config{processor} ne "386" -}]
    $WPASM_x86=wp_block.c wp-mmx.S
    $WPDEF_x86=WHIRLPOOL_ASM
  ENDIF
  $WPASM_x86_64=wp-x86_64.s
  $WPDEF_x86_64=WHIRLPOOL_ASM

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$WPASM_{- $target{asm_arch} -}]
    $WPASM=$WPASM_{- $target{asm_arch} -}
    $WPDEF=$WPDEF_{- $target{asm_arch} -}
  ENDIF
ENDIF

SOURCE[../../libcrypto]=wp_dgst.c $WPASM
DEFINE[../../libcrypto]=$WPDEF

# When all deprecated symbols are removed, libcrypto doesn't export the
# WHIRLPOOL functions, so we must include them directly in liblegacy.a
IF[{- $disabled{'deprecated-3.0'} && !$disabled{module} && !$disabled{shared} -}]
  SOURCE[../../providers/liblegacy.a]=wp_dgst.c $WPASM
  DEFINE[../../providers/liblegacy.a]=$WPDEF
ENDIF

GENERATE[wp-mmx.S]=asm/wp-mmx.pl
DEPEND[wp-mmx.S]=../perlasm/x86asm.pl

GENERATE[wp-x86_64.s]=asm/wp-x86_64.pl
