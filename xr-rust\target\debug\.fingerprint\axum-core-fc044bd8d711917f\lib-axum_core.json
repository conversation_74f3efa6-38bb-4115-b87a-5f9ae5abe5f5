{"rustc": 3926191382657067107, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 11876527447619405325, "path": 17485593943101474765, "deps": [[784494742817713399, "tower_service", false, 17760344758944073368], [1906322745568073236, "pin_project_lite", false, 10639121882622035872], [2517136641825875337, "sync_wrapper", false, 16571654035992993093], [7712452662827335977, "tower_layer", false, 14496693624052965494], [7858942147296547339, "rustversion", false, 1019206695805905597], [8606274917505247608, "tracing", false, 7681878162268537011], [9010263965687315507, "http", false, 14911240995071956450], [10229185211513642314, "mime", false, 17547520712708162187], [10629569228670356391, "futures_util", false, 7860309331142654812], [11946729385090170470, "async_trait", false, 17474458987994229636], [14084095096285906100, "http_body", false, 10450158204625247914], [16066129441945555748, "bytes", false, 13121093402027207528], [16900715236047033623, "http_body_util", false, 17125643490468631981]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-fc044bd8d711917f\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}