use axum::http::StatusCode;
use axum::response::{IntoResponse, Response};
use axum::Json;
use serde_json::json;
use thiserror::Error;

/// 🚨 科学级错误处理系统
/// 
/// 特性：
/// - 🎯 类型安全的错误定义
/// - 🔄 自动HTTP状态码映射
/// - 📊 结构化错误响应
/// - 🛡️ 敏感信息保护
#[derive(Error, Debug)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(#[from] mysql_async::Error),
    
    #[error("HTTP请求错误: {0}")]
    Http(#[from] reqwest::Error),
    
    #[error("配置错误: {0}")]
    Config(#[from] config::ConfigError),
    
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),
    
    // #[error("爬虫错误: {0}")]
    // Crawler(String),
    
    // #[error("验证错误: {0}")]
    // Validation(String),
    
    // #[error("通用错误: {0}")]
    // Generic(String),
}

/// 🔄 自动HTTP响应转换
/// 
/// 将内部错误类型自动转换为合适的HTTP响应
impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::Database(_) => (
                StatusCode::INTERNAL_SERVER_ERROR,
                "数据库操作失败"
            ),
            AppError::Http(_) => (
                StatusCode::BAD_GATEWAY,
                "外部服务请求失败"
            ),
            AppError::Config(_) => (
                StatusCode::INTERNAL_SERVER_ERROR,
                "系统配置错误"
            ),
            AppError::Serialization(_) => (
                StatusCode::BAD_REQUEST,
                "数据格式错误"
            ),
            AppError::Io(_) => (
                StatusCode::INTERNAL_SERVER_ERROR,
                "文件操作失败"
            ),
            // AppError::Crawler(_) => (
            //     StatusCode::BAD_GATEWAY,
            //     "爬虫服务异常"
            // ),
            // AppError::Validation(_) => (
            //     StatusCode::BAD_REQUEST,
            //     "参数验证失败"
            // ),
            // AppError::Generic(_) => (
            //     StatusCode::INTERNAL_SERVER_ERROR,
            //     "系统内部错误"
            // ),
        };

        let body = Json(json!({
            "success": false,
            "error": error_message,
            "details": self.to_string(),
            "timestamp": chrono::Utc::now(),
            "rust_powered": true
        }));

        (status, body).into_response()
    }
}

// pub type Result<T> = std::result::Result<T, AppError>;
