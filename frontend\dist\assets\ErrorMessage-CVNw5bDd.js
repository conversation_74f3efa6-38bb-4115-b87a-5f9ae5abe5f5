import{x as I,y as T,z as j,A as M,r as c,c as w,a as y,o as i,f as B,w as p,d as P,t as N,g as h,n as R,j as k,h as S,k as b}from"./index-BpqYk2C3.js";function Q(){const t=T(j,null);return t===null&&I("use-message","No outer <n-message-provider /> founded. See prerequisite in https://www.naiveui.com/en-US/os-theme/components/message for more details. If you want to use `useMessage` outside setup, please check https://www.naiveui.com/zh-CN/os-theme/components/message#Q-&-A."),t}const q="/api";async function D(t,n={}){const r={headers:{"Content-Type":"application/json",...n.headers},...n};try{const a=await fetch(`${q}${t}`,r);if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const e=await a.json();if(e.code!==200)throw new Error(e.message||"请求失败");return e}catch(a){throw console.error("API请求错误:",a),a}}function _(t,n={}){const a=new URLSearchParams(n).toString(),e=a?`${t}?${a}`:t;return D(e,{method:"GET"})}const x={getList(t={}){return _("/gallery/list",t)},getDetail(t){return _(`/gallery/detail/${t}`)}};function F(){return _("/ping")}const J=M("gallery",()=>{const t=c([]),n=c(null),r=c(!1),a=c(null),e=c({currentPage:1,perPage:15,total:0,totalPages:0}),l=c("latest"),d=w(()=>t.value.length>0),v=w(()=>e.value.currentPage<e.value.totalPages);async function g(o={}){r.value=!0,a.value=null;try{const s={page:e.value.currentPage||1,limit:e.value.perPage||15,sort:l.value||"latest",...o},f=await x.getList(s);if(t.value=f.data.list,f.data.pagination){const u=f.data.pagination;e.value={currentPage:u.current_page||u.currentPage||e.value.currentPage||1,perPage:u.per_page||u.perPage||e.value.perPage||15,total:u.total||0,totalPages:u.total_pages||u.totalPages||0}}return f.data}catch(s){throw a.value=s.message,console.error("获取图库列表失败:",s),s}finally{r.value=!1}}async function C(o){r.value=!0,a.value=null;try{const s=await x.getDetail(o);return n.value=s.data,s.data}catch(s){throw a.value=s.message,console.error("获取图库详情失败:",s),s}finally{r.value=!1}}function E(){return l.value=l.value==="latest"?"oldest":"latest",e.value.currentPage=1,g()}function m(o){if(o>=1&&o<=e.value.totalPages)return e.value.currentPage=o,g()}function z(){if(v.value)return m(e.value.currentPage+1)}function A(){if(e.value.currentPage>1)return m(e.value.currentPage-1)}function L(o){return e.value.perPage=o,e.value.currentPage=1,g()}function G(){t.value=[],n.value=null,r.value=!1,a.value=null,e.value={currentPage:1,perPage:15,total:0,totalPages:0},l.value="latest"}return{galleries:t,currentGallery:n,loading:r,error:a,pagination:e,sortOrder:l,hasGalleries:d,hasMore:v,fetchGalleries:g,fetchGalleryDetail:C,toggleSort:E,goToPage:m,nextPage:z,prevPage:A,changePageSize:L,reset:G}}),$=(t,n)=>{const r=t.__vccOpts||t;for(const[a,e]of n)r[a]=e;return r},U={key:0},V={__name:"Loading",props:{size:{type:String,default:"medium",validator:t=>["small","medium","large"].includes(t)},text:{type:String,default:""},fullScreen:{type:Boolean,default:!1}},setup(t){return(n,r)=>{const a=h("n-spin");return i(),y("div",{class:R(["loading-container",{"full-screen":t.fullScreen}])},[B(a,{size:t.size,show:!0},{description:p(()=>[t.text?(i(),y("span",U,N(t.text),1)):P("",!0)]),_:1},8,["size"])],2)}}},W=$(V,[["__scopeId","data-v-f9aa7520"]]),O={class:"error-container"},H={__name:"ErrorMessage",props:{title:{type:String,default:"出错了"},description:{type:String,default:"请稍后重试"},showRetry:{type:Boolean,default:!0},showBack:{type:Boolean,default:!1}},emits:["retry"],setup(t){const n=b();function r(){n.back()}return(a,e)=>{const l=h("n-button"),d=h("n-result");return i(),y("div",O,[B(d,{status:"error",title:t.title,description:t.description},{footer:p(()=>[t.showRetry?(i(),k(l,{key:0,type:"primary",onClick:e[0]||(e[0]=v=>a.$emit("retry"))},{default:p(()=>e[1]||(e[1]=[S(" 重试 ",-1)])),_:1,__:[1]})):P("",!0),t.showBack?(i(),k(l,{key:1,onClick:r,style:{"margin-left":"12px"}},{default:p(()=>e[2]||(e[2]=[S(" 返回 ",-1)])),_:1,__:[2]})):P("",!0)]),_:1},8,["title","description"])])}}},X=$(H,[["__scopeId","data-v-454771c0"]]);export{X as E,W as L,$ as _,Q as a,F as t,J as u};
