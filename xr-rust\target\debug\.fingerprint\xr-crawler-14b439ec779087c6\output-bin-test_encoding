{"$message_type":"diagnostic","message":"unknown start of token: \\u{ac}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":608,"byte_end":610,"line_start":10,"line_end":10,"column_start":95,"column_end":96,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":95,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{ac}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:95\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bc}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":610,"byte_end":612,"line_start":10,"line_end":10,"column_start":96,"column_end":97,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":96,"highlight_end":97}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bc}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:96\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{ab}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":616,"byte_end":618,"line_start":10,"line_end":10,"column_start":99,"column_end":100,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":99,"highlight_end":100}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{ab}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:99\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mŸè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b9}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":624,"byte_end":626,"line_start":10,"line_end":10,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b9}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:103\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mçº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{ab}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":628,"byte_end":630,"line_start":10,"line_end":10,"column_start":105,"column_end":106,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":105,"highlight_end":106}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{ab}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:105\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{af}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":632,"byte_end":634,"line_start":10,"line_end":10,"column_start":107,"column_end":108,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":107,"highlight_end":108}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{af}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:107\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b1}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":634,"byte_end":636,"line_start":10,"line_end":10,"column_start":108,"column_end":109,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":108,"highlight_end":109}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b1}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:108\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"prefix `æƒ` is unknown","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":636,"byte_end":640,"line_start":10,"line_end":10,"column_start":109,"column_end":111,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":109,"highlight_end":111}],"label":"unknown prefix","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"prefixed identifiers and literals are reserved since Rust 2021","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider inserting whitespace here","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":640,"byte_end":640,"line_start":10,"line_end":10,"column_start":111,"column_end":111,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":111,"highlight_end":111}],"label":null,"suggested_replacement":" ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: prefix `æƒ` is unknown\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:109\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown prefix\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: prefixed identifiers and literals are reserved since Rust 2021\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider inserting whitespace here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ\u001b[0m\u001b[0m\u001b[38;5;10m \u001b[0m\u001b[0m'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                              \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{2020}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":643,"byte_end":646,"line_start":10,"line_end":10,"column_start":113,"column_end":114,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10549_æ¨¡ç‰¹æ²å¨œå¨œç§æˆ¿æ€§æ„Ÿè–„çº±éœ²ç™½è‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",","highlight_start":113,"highlight_end":114}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{2020}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:10:113\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m‰²æƒ…è¶£å†…è¡£ç§€é­\"é¬¼èº«ææƒ¹ç«è¯±æƒ'å†™çœŸ75P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":679,"byte_end":681,"line_start":11,"line_end":11,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{20ac}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":681,"byte_end":684,"line_start":11,"line_end":11,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{20ac}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bd}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":692,"byte_end":694,"line_start":11,"line_end":11,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bd}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"character literal may only contain one codepoint","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":694,"byte_end":772,"line_start":11,"line_end":11,"column_start":25,"column_end":68,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":25,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you meant to write a string literal, use double quotes","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":694,"byte_end":695,"line_start":11,"line_end":11,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":"\"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\bin\\test_encoding.rs","byte_start":771,"byte_end":772,"line_start":11,"line_end":11,"column_start":67,"column_end":68,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":67,"highlight_end":68}],"label":null,"suggested_replacement":"\"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: character literal may only contain one codepoint\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you meant to write a string literal, use double quotes\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½\u001b[0m\u001b[0m\u001b[38;5;9m'\u001b[0m\u001b[0m]No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»\u001b[0m\u001b[0m\u001b[38;5;9m'\u001b[0m\u001b[0mè‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½\u001b[0m\u001b[0m\u001b[38;5;10m\"\u001b[0m\u001b[0m]No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»\u001b[0m\u001b[0m\u001b[38;5;10m\"\u001b[0m\u001b[0mè‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{2030}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":774,"byte_end":777,"line_start":11,"line_end":11,"column_start":69,"column_end":70,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":69,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{2030}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:69\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b2}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":777,"byte_end":779,"line_start":11,"line_end":11,"column_start":70,"column_end":71,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":70,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b2}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:70\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{ad}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":783,"byte_end":785,"line_start":11,"line_end":11,"column_start":73,"column_end":74,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":73,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{ad}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:73\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a3}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":787,"byte_end":789,"line_start":11,"line_end":11,"column_start":75,"column_end":76,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":75,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a3}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:75\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{2026}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":794,"byte_end":797,"line_start":11,"line_end":11,"column_start":78,"column_end":79,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":78,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{2026}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:78\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b6}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":799,"byte_end":801,"line_start":11,"line_end":11,"column_start":80,"column_end":81,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":80,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b6}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:80\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{2026}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":801,"byte_end":804,"line_start":11,"line_end":11,"column_start":81,"column_end":82,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":81,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{2026}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:81\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{2013}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":806,"byte_end":809,"line_start":11,"line_end":11,"column_start":83,"column_end":84,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":83,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Unicode character '–' (En Dash) looks like '-' (Minus/Hyphen), but it is not","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":806,"byte_end":809,"line_start":11,"line_end":11,"column_start":83,"column_end":84,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":83,"highlight_end":84}],"label":null,"suggested_replacement":"-","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{2013}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:83\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: Unicode character '–' (En Dash) looks like '-' (Minus/Hyphen), but it is not\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…\u001b[0m\u001b[0m\u001b[38;5;9mè–„\u001b[0m\u001b[0mé»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è\u001b[0m\u001b[0m\u001b[38;5;10m-\u001b[0m\u001b[0m„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{201e}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":809,"byte_end":812,"line_start":11,"line_end":11,"column_start":84,"column_end":85,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":84,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{201e}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:84\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bb}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":814,"byte_end":816,"line_start":11,"line_end":11,"column_start":86,"column_end":87,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":86,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bb}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:86\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b8}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":819,"byte_end":821,"line_start":11,"line_end":11,"column_start":89,"column_end":90,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":89,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Unicode character '¸' (Cedilla) looks like ',' (Comma), but it is not","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":819,"byte_end":821,"line_start":11,"line_end":11,"column_start":89,"column_end":90,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":89,"highlight_end":90}],"label":null,"suggested_replacement":",","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b8}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:89\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: Unicode character '¸' (Cedilla) looks like ',' (Comma), but it is not\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'\u001b[0m\u001b[0m\u001b[38;5;9mä¸\u001b[0m\u001b[0mç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä\u001b[0m\u001b[0m\u001b[38;5;10m,\u001b[0m\u001b[0mç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":823,"byte_end":825,"line_start":11,"line_end":11,"column_start":91,"column_end":92,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":91,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:91\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{20ac}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":825,"byte_end":828,"line_start":11,"line_end":11,"column_start":92,"column_end":93,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":92,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{20ac}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:92\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mšå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{203a}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":830,"byte_end":833,"line_start":11,"line_end":11,"column_start":94,"column_end":95,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":94,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Unicode character '›' (Single Right-Pointing Angle Quotation Mark) looks like '>' (Greater-Than Sign), but it is not","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":830,"byte_end":833,"line_start":11,"line_end":11,"column_start":94,"column_end":95,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":94,"highlight_end":95}],"label":null,"suggested_replacement":">","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{203a}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:94\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: Unicode character '›' (Single Right-Pointing Angle Quotation Mark) looks like '>' (Greater-Than Sign), but it is not\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€\u001b[0m\u001b[0m\u001b[38;5;9mæ›¼\u001b[0m\u001b[0må¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[38;5;10m>\u001b[0m\u001b[0m¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bc}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":833,"byte_end":835,"line_start":11,"line_end":11,"column_start":95,"column_end":96,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":95,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bc}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:95\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a6}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":837,"byte_end":839,"line_start":11,"line_end":11,"column_start":97,"column_end":98,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":97,"highlight_end":98}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a6}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:97\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mžæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{ab}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":846,"byte_end":848,"line_start":11,"line_end":11,"column_start":101,"column_end":102,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":101,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{ab}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:101\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mæ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":850,"byte_end":852,"line_start":11,"line_end":11,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:103\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mŸé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bf}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":852,"byte_end":854,"line_start":11,"line_end":11,"column_start":104,"column_end":105,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":104,"highlight_end":105}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bf}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:104\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{af}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":856,"byte_end":858,"line_start":11,"line_end":11,"column_start":106,"column_end":107,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":106,"highlight_end":107}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{af}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:106\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b1}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":858,"byte_end":860,"line_start":11,"line_end":11,"column_start":107,"column_end":108,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":107,"highlight_end":108}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b1}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:107\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mè‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"prefix `æƒ` is unknown","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":860,"byte_end":864,"line_start":11,"line_end":11,"column_start":108,"column_end":110,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":108,"highlight_end":110}],"label":"unknown prefix","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"prefixed identifiers and literals are reserved since Rust 2021","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if you meant to write a string literal, use double quotes","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":816,"byte_end":817,"line_start":11,"line_end":11,"column_start":87,"column_end":88,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":87,"highlight_end":88}],"label":null,"suggested_replacement":"\"","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\bin\\test_encoding.rs","byte_start":864,"byte_end":865,"line_start":11,"line_end":11,"column_start":110,"column_end":111,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":110,"highlight_end":111}],"label":null,"suggested_replacement":"\"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: prefix `æƒ` is unknown\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:108\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mé…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown prefix\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: prefixed identifiers and literals are reserved since Rust 2021\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you meant to write a string literal, use double quotes\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é\u001b[0m\u001b[0m\u001b[38;5;9m»\u001b[0m\u001b[0m'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æ\u001b[0m\u001b[0m\u001b[38;5;9mƒ\u001b[0m\u001b[0m'å†™çœŸ50P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»\u001b[0m\u001b[0m\u001b[38;5;10m\"\u001b[0m\u001b[0mä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ\u001b[0m\u001b[0m\u001b[38;5;10m\"\u001b[0m\u001b[0må†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{2020}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":867,"byte_end":870,"line_start":11,"line_end":11,"column_start":112,"column_end":113,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10548_æ–°äººæ¨¡ç‰¹ä½³å¤šå®ï½žæ€§æ„Ÿé»'è‰²çŸ­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",","highlight_start":112,"highlight_end":113}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{2020}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:11:112\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m­è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¯±æƒ'å†™çœŸ50P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":903,"byte_end":905,"line_start":12,"line_end":12,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{20ac}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":905,"byte_end":908,"line_start":12,"line_end":12,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{20ac}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bd}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":916,"byte_end":918,"line_start":12,"line_end":12,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bd}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"character literal may only contain one codepoint","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":918,"byte_end":1023,"line_start":12,"line_end":12,"column_start":25,"column_end":79,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":25,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you meant to write a string literal, use double quotes","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":918,"byte_end":1023,"line_start":12,"line_end":12,"column_start":25,"column_end":79,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":25,"highlight_end":79}],"label":null,"suggested_replacement":"\"]No.10547_æ¨¡ç‰¹å\\\"å®‰çªæ€§æ„Ÿè—è\\\"è‰²ç¤¼è£™é…è¶…è–„é»\"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: character literal may only contain one codepoint\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you meant to write a string literal, use double quotes\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½\u001b[0m\u001b[0m\u001b[38;5;9m']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½\u001b[0m\u001b[0m\u001b[38;5;10m\"]No.10547_æ¨¡ç‰¹å\\\"å®‰çªæ€§æ„Ÿè—è\\\"è‰²ç¤¼è£™é…è¶…è–„é»\"\u001b[0m\u001b[0mä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b8}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1025,"byte_end":1027,"line_start":12,"line_end":12,"column_start":80,"column_end":81,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":80,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Unicode character '¸' (Cedilla) looks like ',' (Comma), but it is not","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1025,"byte_end":1027,"line_start":12,"line_end":12,"column_start":80,"column_end":81,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":80,"highlight_end":81}],"label":null,"suggested_replacement":",","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b8}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:80\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: Unicode character '¸' (Cedilla) looks like ',' (Comma), but it is not\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä\u001b[0m\u001b[0m\u001b[38;5;9m¸ç\u001b[0m\u001b[0m§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä\u001b[0m\u001b[0m\u001b[38;5;10m,\u001b[0m\u001b[0mç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1029,"byte_end":1031,"line_start":12,"line_end":12,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:82\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{20ac}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1031,"byte_end":1034,"line_start":12,"line_end":12,"column_start":83,"column_end":84,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":83,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{20ac}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:83\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0må®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{203a}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1036,"byte_end":1039,"line_start":12,"line_end":12,"column_start":85,"column_end":86,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":85,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Unicode character '›' (Single Right-Pointing Angle Quotation Mark) looks like '>' (Greater-Than Sign), but it is not","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1036,"byte_end":1039,"line_start":12,"line_end":12,"column_start":85,"column_end":86,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":85,"highlight_end":86}],"label":null,"suggested_replacement":">","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{203a}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:85\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: Unicode character '›' (Single Right-Pointing Angle Quotation Mark) looks like '>' (Greater-Than Sign), but it is not\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[38;5;9m›¼å\u001b[0m\u001b[0m¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ\u001b[0m\u001b[0m\u001b[38;5;10m>\u001b[0m\u001b[0m¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bc}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1039,"byte_end":1041,"line_start":12,"line_end":12,"column_start":86,"column_end":87,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":86,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bc}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:86\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mçªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a6}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1043,"byte_end":1045,"line_start":12,"line_end":12,"column_start":88,"column_end":89,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":88,"highlight_end":89}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a6}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:88\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{ab}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1052,"byte_end":1054,"line_start":12,"line_end":12,"column_start":92,"column_end":93,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":92,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{ab}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:92\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1056,"byte_end":1058,"line_start":12,"line_end":12,"column_start":94,"column_end":95,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":94,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:94\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bf}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1058,"byte_end":1060,"line_start":12,"line_end":12,"column_start":95,"column_end":96,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":95,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bf}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:95\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bf}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1062,"byte_end":1064,"line_start":12,"line_end":12,"column_start":97,"column_end":98,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":97,"highlight_end":98}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bf}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:97\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1064,"byte_end":1066,"line_start":12,"line_end":12,"column_start":98,"column_end":99,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":98,"highlight_end":99}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Unicode character '·' (Middle Dot) looks like '.' (Period), but it is not","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1064,"byte_end":1066,"line_start":12,"line_end":12,"column_start":98,"column_end":99,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":98,"highlight_end":99}],"label":null,"suggested_replacement":".","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:98\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mè‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: Unicode character '·' (Middle Dot) looks like '.' (Period), but it is not\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿\u001b[0m\u001b[0m\u001b[38;5;9m·ä\u001b[0m\u001b[0mººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿\u001b[0m\u001b[0m\u001b[38;5;10m.\u001b[0m\u001b[0mäººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{af}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1074,"byte_end":1076,"line_start":12,"line_end":12,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{af}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:103\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{b1}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1076,"byte_end":1078,"line_start":12,"line_end":12,"column_start":104,"column_end":105,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":104,"highlight_end":105}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{b1}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:104\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mè£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"prefix `æƒ` is unknown","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1078,"byte_end":1082,"line_start":12,"line_end":12,"column_start":105,"column_end":107,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":105,"highlight_end":107}],"label":"unknown prefix","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"prefixed identifiers and literals are reserved since Rust 2021","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider inserting whitespace here","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1082,"byte_end":1082,"line_start":12,"line_end":12,"column_start":107,"column_end":107,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":107,"highlight_end":107}],"label":null,"suggested_replacement":" ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: prefix `æƒ` is unknown\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:105\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown prefix\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: prefixed identifiers and literals are reserved since Rust 2021\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider inserting whitespace here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ\u001b[0m\u001b[0m\u001b[38;5;10m \u001b[0m\u001b[0m'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                           \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{2020}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1085,"byte_end":1088,"line_start":12,"line_end":12,"column_start":109,"column_end":110,"is_primary":true,"text":[{"text":"        \"[XiuRenç§€äººç½']No.10547_æ¨¡ç‰¹å\"å®‰çªæ€§æ„Ÿè—è\"è‰²ç¤¼è£™é…è¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",","highlight_start":109,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{2020}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:12:109\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mè¶…è–„é»'ä¸ç§€æ›¼å¦™èº«å§¿è¿·äººè¯±æƒ'å†™çœŸ85P\",\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1259,"byte_end":1260,"line_start":18,"line_end":18,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"        println!(\"\\n🔍 测试用例 {}: \", i + 1);","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:18:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        println!(\"\\n🔍 测试用例 {}: \", i + 1);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"prefix `编码已修复` is unknown","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1724,"byte_end":1739,"line_start":30,"line_end":30,"column_start":25,"column_end":30,"is_primary":true,"text":[{"text":"            println!(\"✅ 编码已修复\");","highlight_start":25,"highlight_end":30}],"label":"unknown prefix","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"prefixed identifiers and literals are reserved since Rust 2021","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider inserting whitespace here","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1739,"byte_end":1739,"line_start":30,"line_end":30,"column_start":30,"column_end":30,"is_primary":true,"text":[{"text":"            println!(\"✅ 编码已修复\");","highlight_start":30,"highlight_end":30}],"label":null,"suggested_replacement":" ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: prefix `编码已修复` is unknown\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:30:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            println!(\"✅ 编码已修复\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown prefix\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: prefixed identifiers and literals are reserved since Rust 2021\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider inserting whitespace here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            println!(\"✅ 编码已修复\u001b[0m\u001b[0m\u001b[38;5;10m \u001b[0m\u001b[0m\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"prefix `无需修复或修复失败` is unknown","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1790,"byte_end":1817,"line_start":32,"line_end":32,"column_start":27,"column_end":36,"is_primary":true,"text":[{"text":"            println!(\"⚠️  无需修复或修复失败\");","highlight_start":27,"highlight_end":36}],"label":"unknown prefix","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"prefixed identifiers and literals are reserved since Rust 2021","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider inserting whitespace here","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1817,"byte_end":1817,"line_start":32,"line_end":32,"column_start":36,"column_end":36,"is_primary":true,"text":[{"text":"            println!(\"⚠️  无需修复或修复失败\");","highlight_start":36,"highlight_end":36}],"label":null,"suggested_replacement":" ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: prefix `无需修复或修复失败` is unknown\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:32:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            println!(\"⚠️  无需修复或修复失败\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown prefix\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: prefixed identifiers and literals are reserved since Rust 2021\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider inserting whitespace here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            println!(\"⚠️  无需修复或修复失败\u001b[0m\u001b[0m\u001b[38;5;10m \u001b[0m\u001b[0m\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1856,"byte_end":1857,"line_start":36,"line_end":36,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    println!(\"\\n🎯 测试完成！\");","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:36:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"\\n🎯 测试完成！\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{ff01}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1875,"byte_end":1878,"line_start":36,"line_end":36,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"    println!(\"\\n🎯 测试完成！\");","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Unicode character '！' (Fullwidth Exclamation Mark) looks like '!' (Exclamation Mark), but it is not","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":1875,"byte_end":1878,"line_start":36,"line_end":36,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"    println!(\"\\n🎯 测试完成！\");","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{ff01}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:36:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"\\n🎯 测试完成！\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: Unicode character '！' (Fullwidth Exclamation Mark) looks like '!' (Exclamation Mark), but it is not\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    println!(\"\\n🎯 测试完成！\");\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    println!(\"\\n🎯 测试完成\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"prefix `检测到可能的编码问题` is unknown","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2194,"byte_end":2224,"line_start":50,"line_end":50,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"    println!(\"🔍 检测到可能的编码问题\");","highlight_start":17,"highlight_end":27}],"label":"unknown prefix","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"prefixed identifiers and literals are reserved since Rust 2021","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider inserting whitespace here","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2224,"byte_end":2224,"line_start":50,"line_end":50,"column_start":27,"column_end":27,"is_primary":true,"text":[{"text":"    println!(\"🔍 检测到可能的编码问题\");","highlight_start":27,"highlight_end":27}],"label":null,"suggested_replacement":" ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: prefix `检测到可能的编码问题` is unknown\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:50:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"🔍 检测到可能的编码问题\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown prefix\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: prefixed identifiers and literals are reserved since Rust 2021\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider inserting whitespace here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    println!(\"🔍 检测到可能的编码问题\u001b[0m\u001b[0m\u001b[38;5;10m \u001b[0m\u001b[0m\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"prefix `编码修复成功` is unknown","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2402,"byte_end":2420,"line_start":55,"line_end":55,"column_start":25,"column_end":31,"is_primary":true,"text":[{"text":"            println!(\"✅ 编码修复成功\");","highlight_start":25,"highlight_end":31}],"label":"unknown prefix","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"prefixed identifiers and literals are reserved since Rust 2021","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider inserting whitespace here","code":null,"level":"help","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2420,"byte_end":2420,"line_start":55,"line_end":55,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"            println!(\"✅ 编码修复成功\");","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":" ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: prefix `编码修复成功` is unknown\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:55:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            println!(\"✅ 编码修复成功\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown prefix\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: prefixed identifiers and literals are reserved since Rust 2021\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider inserting whitespace here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            println!(\"✅ 编码修复成功\u001b[0m\u001b[0m\u001b[38;5;10m \u001b[0m\u001b[0m\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{a7}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2612,"byte_end":2614,"line_start":66,"line_end":66,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{a7}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:66:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{20ac}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2614,"byte_end":2617,"line_start":66,"line_end":66,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{20ac}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:66:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unknown start of token: \\u{bd}","code":null,"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2633,"byte_end":2635,"line_start":66,"line_end":66,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unknown start of token: \\u{bd}\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:66:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unterminated character literal","code":{"code":"E0762","explanation":"A character literal wasn't ended with a quote.\n\nErroneous code example:\n\n```compile_fail,E0762\nstatic C: char = '●; // error!\n```\n\nTo fix this error, add the missing quote:\n\n```\nstatic C: char = '●'; // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\bin\\test_encoding.rs","byte_start":2635,"byte_end":2660,"line_start":66,"line_end":66,"column_start":26,"column_end":44,"is_primary":true,"text":[{"text":"        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特","highlight_start":26,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0762]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unterminated character literal\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\bin\\test_encoding.rs:66:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"ç§€\", \"äºº\", \"ç½'\", \"æ¨¡\", \"ç‰¹\", // 秀人网模特\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 66 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 66 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0762`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0762`.\u001b[0m\n"}
