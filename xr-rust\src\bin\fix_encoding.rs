use anyhow::Result;
use mysql_async::{Pool, Row};
use mysql_async::prelude::*;
use tracing::{info, warn, error};
use std::collections::HashMap;

/// 🔧 中文编码修复工具
/// 
/// 功能：
/// - 🔍 检测数据库中的编码错误数据
/// - 🔄 修复双重UTF-8编码问题
/// - 📊 统计修复结果
/// - 🛡️ 安全的批量更新操作

#[tokio::main]
async fn main() -> Result<()> {
    // 🚀 初始化日志系统
    tracing_subscriber::fmt()
        .with_env_filter("info")
        .init();

    info!("🔧 开始中文编码修复任务");

    // 🔗 连接数据库
    let database_url = "mysql://root:wangcong@101.32.40.152:11436/zz?charset=utf8mb4&collation=utf8mb4_unicode_ci";
    let opts = mysql_async::Opts::from_url(database_url)?;
    let pool = Pool::new(opts);
    let mut conn = pool.get_conn().await?;

    // 📊 统计信息
    let mut stats = FixStats::new();

    // 🔍 查询所有需要修复的记录
    info!("🔍 查询需要修复的记录...");
    let rows: Vec<Row> = conn.query(
        "SELECT id, xrid, title FROM xr WHERE title IS NOT NULL AND title != ''"
    ).await?;

    info!("📊 找到 {} 条记录需要检查", rows.len());

    // 🔄 批量修复处理
    for row in rows {
        let id: i64 = row.get("id").unwrap();
        let xrid: i32 = row.get("xrid").unwrap();
        let title: String = row.get("title").unwrap();

        stats.total_checked += 1;

        // 🔍 检测是否需要修复
        if let Some(fixed_title) = fix_encoding_if_needed(&title) {
            info!("🔧 修复记录 ID={}, XRID={}", id, xrid);
            info!("   原始: {}", title);
            info!("   修复: {}", fixed_title);

            // 🔄 更新数据库
            match conn.exec_drop(
                "UPDATE xr SET title = ? WHERE id = ?",
                (&fixed_title, id)
            ).await {
                Ok(_) => {
                    stats.fixed_count += 1;
                    info!("✅ 修复成功");
                }
                Err(e) => {
                    stats.error_count += 1;
                    error!("❌ 修复失败: {}", e);
                }
            }
        } else {
            stats.no_fix_needed += 1;
        }

        // 🎯 每100条记录输出一次进度
        if stats.total_checked % 100 == 0 {
            info!("📊 进度: {}/{} 已检查, {} 已修复, {} 错误", 
                  stats.total_checked, rows.len(), stats.fixed_count, stats.error_count);
        }
    }

    // 📊 输出最终统计
    info!("🎯 修复任务完成！");
    info!("📊 统计结果:");
    info!("   总检查数: {}", stats.total_checked);
    info!("   修复成功: {}", stats.fixed_count);
    info!("   无需修复: {}", stats.no_fix_needed);
    info!("   修复失败: {}", stats.error_count);

    Ok(())
}

/// 📊 修复统计信息
#[derive(Debug, Default)]
struct FixStats {
    total_checked: usize,
    fixed_count: usize,
    no_fix_needed: usize,
    error_count: usize,
}

impl FixStats {
    fn new() -> Self {
        Self::default()
    }
}

/// 🔍 检测并修复编码问题
/// 
/// 返回值：
/// - Some(fixed_string): 需要修复，返回修复后的字符串
/// - None: 不需要修复
fn fix_encoding_if_needed(input: &str) -> Option<String> {
    // 🔍 检测是否包含典型的双重UTF-8编码特征
    if !contains_double_encoding_patterns(input) {
        return None;
    }

    // 🔄 尝试修复双重UTF-8编码
    if let Some(fixed) = fix_double_utf8_encoding(input) {
        // 🧪 验证修复结果是否更好
        if is_better_chinese_text(&fixed, input) {
            return Some(fixed);
        }
    }

    None
}

/// 🔍 检测双重编码特征
fn contains_double_encoding_patterns(text: &str) -> bool {
    // 常见的双重UTF-8编码特征
    let patterns = [
        "ç§€", "äºº", "ç½'", "æ¨¡", "ç‰¹", // 秀人网模特
        "æ€§", "æ„Ÿ", "è‰²", "è£…", "æ‰®", // 性感色装扮
        "å†…", "è¡£", "ç§€", "èº«", "æ", // 内衣秀身材
        "è¯±", "æƒ'", "å†™", "çœŸ", "ç…§", // 诱惑写真照
    ];

    patterns.iter().any(|pattern| text.contains(pattern))
}

/// 🔄 修复双重UTF-8编码
fn fix_double_utf8_encoding(input: &str) -> Option<String> {
    // 🔄 方法1: 将UTF-8字节重新解释为Latin1，然后解码为UTF-8
    if let Some(fixed) = try_latin1_fix(input) {
        return Some(fixed);
    }

    // 🔄 方法2: 尝试其他编码修复方法
    try_other_encoding_fixes(input)
}

/// 🔄 Latin1修复方法
fn try_latin1_fix(input: &str) -> Option<String> {
    use std::str;
    
    // 将字符串转换为字节数组
    let bytes = input.as_bytes();
    
    // 尝试将每个字节重新解释为Latin1字符，然后转换为UTF-8
    let mut latin1_bytes = Vec::new();
    let mut i = 0;
    
    while i < bytes.len() {
        if let Ok(ch) = str::from_utf8(&bytes[i..i+1]) {
            if let Some(c) = ch.chars().next() {
                if c as u32 <= 255 {
                    latin1_bytes.push(c as u8);
                } else {
                    // 对于超出Latin1范围的字符，尝试UTF-8解码
                    let mut j = i;
                    while j < bytes.len() && bytes[j] & 0x80 != 0 {
                        j += 1;
                    }
                    if j > i {
                        latin1_bytes.extend_from_slice(&bytes[i..j]);
                        i = j - 1;
                    } else {
                        latin1_bytes.push(bytes[i]);
                    }
                }
            }
        } else {
            latin1_bytes.push(bytes[i]);
        }
        i += 1;
    }
    
    // 尝试将结果解释为UTF-8
    String::from_utf8(latin1_bytes).ok()
}

/// 🔄 其他编码修复方法
fn try_other_encoding_fixes(input: &str) -> Option<String> {
    // 这里可以添加其他编码修复策略
    // 例如：GBK转UTF-8等
    None
}

/// 🧪 判断修复后的文本是否更好
fn is_better_chinese_text(fixed: &str, original: &str) -> bool {
    // 🔍 检查是否包含更多的中文字符
    let fixed_chinese_count = count_chinese_chars(fixed);
    let original_chinese_count = count_chinese_chars(original);
    
    // 🎯 如果修复后的中文字符数量明显增加，认为修复有效
    fixed_chinese_count > original_chinese_count && fixed_chinese_count > 5
}

/// 🔢 统计中文字符数量
fn count_chinese_chars(text: &str) -> usize {
    text.chars()
        .filter(|&c| {
            let code = c as u32;
            // 基本中文字符范围
            (code >= 0x4E00 && code <= 0x9FFF) ||
            // 中文标点符号
            (code >= 0x3000 && code <= 0x303F) ||
            // 其他中文相关字符
            (code >= 0xFF00 && code <= 0xFFEF)
        })
        .count()
}
