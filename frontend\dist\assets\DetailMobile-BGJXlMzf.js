import{c as x,l as G,i as b,m as k,k as D,p as w,a as l,b as a,d as c,u as r,t as i,j as y,F as M,e as E,o as s}from"./index-BpqYk2C3.js";import{_ as X,a as I,u as L,L as B,E as Y}from"./ErrorMessage-CVNw5bDd.js";import{i as C}from"./device-Bim2jCTP.js";const S={class:"detail-mobile"},$={class:"mobile-header"},N={class:"header-content"},R={key:0,class:"header-info"},z={class:"gallery-title"},F={class:"gallery-meta"},V={class:"image-count"},j={class:"mobile-content"},H={key:2,class:"waterfall-container"},T=["src","alt"],U={class:"image-number"},q={key:0,class:"mobile-navigation"},A={key:0,class:"nav-item"},J=["src","alt"],K={class:"nav-info"},O={class:"nav-id"},P={key:1,class:"nav-item"},Q=["src","alt"],W={class:"nav-info"},Z={class:"nav-id"},ee={__name:"DetailMobile",setup(te){const d=k(),u=D(),v=I(),t=L(),g=x(()=>{var e;const n=(e=t.currentGallery)==null?void 0:e.navigation;return n&&(n.prev||n.next)});async function m(){const n=parseInt(d.params.xrid);if(!n){v.error("无效的图库ID");return}try{await t.fetchGalleryDetail(n)}catch{v.error("加载详情失败")}}function h(){try{u.push("/")}catch(n){console.error("路由跳转失败:",n),v.error("返回首页失败")}}function _(n){u.push({name:"DetailMobile",params:{xrid:n}})}function p(n){console.log("图片加载成功:",n.target.src)}function f(n){console.error("图片加载失败:",n.target.src)}return G(()=>d.params.xrid,()=>{d.name==="DetailMobile"&&m()}),b(()=>{if(C()){const e=d.params.xrid;console.log(`检测到桌面设备，从移动端详情页跳转到桌面端详情页: ${e}`),u.replace({name:"DetailDesktop",params:{xrid:e}});return}m(),document.body.style.overflowX="hidden",document.documentElement.style.overflowX="hidden";const n=e=>{Math.abs(e.deltaX)>Math.abs(e.deltaY)&&e.preventDefault()};document.addEventListener("wheel",n,{passive:!1}),document.addEventListener("touchmove",e=>{if(e.touches.length===1){const o=e.touches[0];o.clientY,Math.abs(o.clientX-(o.startX||o.clientX))>Math.abs(o.clientY-(o.startY||o.clientY))&&e.preventDefault()}},{passive:!1})}),w(()=>{document.body.style.overflowX="",document.documentElement.style.overflowX=""}),(n,e)=>(s(),l("div",S,[a("header",$,[a("div",N,[a("button",{onClick:h,class:"back-button"}," ← 返回 "),r(t).currentGallery?(s(),l("div",R,[a("h1",z,i(r(t).currentGallery.info.title),1),a("div",F,[a("span",V,i(r(t).currentGallery.images.length)+" 张图片",1)])])):c("",!0)])]),a("main",j,[r(t).loading?(s(),y(B,{key:0,text:"加载中..."})):r(t).error?(s(),y(Y,{key:1,title:r(t).error,description:"无法加载图片","show-back":"",onRetry:m},null,8,["title"])):r(t).currentGallery?(s(),l("div",H,[(s(!0),l(M,null,E(r(t).currentGallery.images,(o,ae)=>(s(),l("div",{key:o.id,class:"waterfall-item"},[a("img",{src:o.reurl,alt:`图片 ${o.order}`,class:"waterfall-image",loading:"lazy",onLoad:p,onError:f},null,40,T),a("div",U,i(o.order),1)]))),128)),g.value?(s(),l("div",q,[r(t).currentGallery.navigation.prev?(s(),l("div",A,[e[2]||(e[2]=a("h3",null,"上一套",-1)),a("div",{class:"nav-card",onClick:e[0]||(e[0]=o=>_(r(t).currentGallery.navigation.prev.xrid))},[a("img",{src:r(t).currentGallery.navigation.prev.cover,alt:r(t).currentGallery.navigation.prev.title,class:"nav-image"},null,8,J),a("div",K,[a("h4",null,i(r(t).currentGallery.navigation.prev.title),1),a("span",O,"ID: "+i(r(t).currentGallery.navigation.prev.xrid),1)])])])):c("",!0),r(t).currentGallery.navigation.next?(s(),l("div",P,[e[3]||(e[3]=a("h3",null,"下一套",-1)),a("div",{class:"nav-card",onClick:e[1]||(e[1]=o=>_(r(t).currentGallery.navigation.next.xrid))},[a("img",{src:r(t).currentGallery.navigation.next.cover,alt:r(t).currentGallery.navigation.next.title,class:"nav-image"},null,8,Q),a("div",W,[a("h4",null,i(r(t).currentGallery.navigation.next.title),1),a("span",Z,"ID: "+i(r(t).currentGallery.navigation.next.xrid),1)])])])):c("",!0)])):c("",!0)])):c("",!0)])]))}},se=X(ee,[["__scopeId","data-v-d5c0a91c"]]);export{se as default};
