import { createApp } from 'vue'
import { createPinia } from 'pinia'
import {
  create,
  NButton,
  NCard,
  NPagination,
  NConfigProvider,
  NMessageProvider,
  NLoadingBarProvider,
  NDialogProvider,
  NNotificationProvider,
  NSpin,
  NResult,
  NGrid,
  NGridItem,
  NImage,
  NSpace,
  NTag,
  NEmpty,
  NIcon,
  NForm,
  NFormItem,
  NInput
} from 'naive-ui'
import App from './App.vue'
import router from './router'
import './styles/main.css'

// 配置Naive UI - 按需引入常用组件
const naive = create({
  components: [
    NButton,
    NCard,
    NPagination,
    NConfigProvider,
    NMessageProvider,
    NLoadingBarProvider,
    NDialogProvider,
    NNotificationProvider,
    NSpin,
    NResult,
    NGrid,
    NGridItem,
    NImage,
    NSpace,
    NTag,
    NEmpty,
    NIcon,
    NForm,
    NFormItem,
    NInput
  ]
})

// 创建应用
const app = createApp(App)
app.use(createPinia())
app.use(router)
app.use(naive)

app.mount('#app')
