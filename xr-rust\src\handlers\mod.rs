use axum::{
    extract::{Query, State},
    response::J<PERSON>,
};
use serde_json::Value;
use std::collections::HashMap;
use tracing::{error, info, warn};

use crate::models::{ApiResponse, SystemStatus};
use crate::AppState;

/// 🏠 系统主页处理器
///
/// 返回系统状态和API文档信息
#[axum::debug_handler]
pub async fn home() -> Json<Value> {
    info!("🏠 访问系统主页");

    let status = SystemStatus::new();
    Json(serde_json::to_value(status).unwrap())
}

/// 📋 获取列表页数据处理器 - 核心API实现
///
/// 这是展示Rust科学级实现的核心API：
/// - 🎯 类型安全的参数处理
/// - ⚡ 零拷贝性能优化
/// - 🛡️ 全面的错误处理
/// - 📊 详细的性能监控
#[axum::debug_handler]
pub async fn get_list(
    Query(params): Query<HashMap<String, String>>,
    State(state): State<AppState>,
) -> Json<Value> {
    // 🎯 智能参数解析
    let page = params
        .get("page")
        .and_then(|p| p.parse::<i32>().ok())
        .unwrap_or(1)
        .max(1); // 确保页码至少为1

    info!("📋 开始处理第{}页列表数据请求", page);

    // 🕷️ 执行爬虫任务
    match state.crawler_service.get_list_page(page).await {
        Ok(items) => {
            let count = items.len();
            info!("✅ 爬虫任务完成: 第{}页获取{}条记录", page, count);

            // 🗄️ 数据库操作（如果可用）
            let saved_count = if let Some(ref db) = state.database {
                match db.batch_create_xr(&items).await {
                    Ok(saved) => {
                        info!("💾 数据库保存成功: 新增{}条记录", saved);
                        saved
                    }
                    Err(e) => {
                        error!("❌ 数据库保存失败: {}", e);
                        // 即使数据库保存失败，也返回爬取的数据
                        let response = ApiResponse::success_with_page(items, page, count);
                        let mut json_value = serde_json::to_value(response).unwrap();
                        json_value["database_error"] = serde_json::Value::String(
                            format!("数据库保存失败: {}", e)
                        );
                        return Json(json_value);
                    }
                }
            } else {
                warn!("⚠️  数据库不可用，仅返回爬取数据");
                0
            };

            // 🎉 构建成功响应
            let response = ApiResponse::success_with_page(items, page, count);
            let mut json_value = serde_json::to_value(response).unwrap();

            // 📊 添加额外统计信息
            json_value["saved_count"] = serde_json::Value::Number(saved_count.into());
            json_value["performance"] = serde_json::json!({
                "rust_powered": true,
                "zero_cost_abstractions": true,
                "memory_safe": true,
                "blazingly_fast": true
            });

            info!("🎉 第{}页请求处理完成: 爬取{}条，保存{}条", page, count, saved_count);
            Json(json_value)
        }
        Err(e) => {
            error!("❌ 爬虫任务失败: 第{}页 - {}", page, e);

            let response: ApiResponse<Vec<crate::models::ListItem>> = ApiResponse::error_with_page(
                format!("爬取第{}页失败: {}", page, e),
                page
            );

            Json(serde_json::to_value(response).unwrap())
        }
    }
}

/// 🖼️ 获取内页图片列表处理器 - 详情页爬取核心API
///
/// 这是Rust版本的内页图片爬取实现：
/// - 🎯 智能任务调度
/// - 🔒 状态管理
/// - 📸 多页面图片提取
/// - 💾 批量数据保存
pub async fn get_img_list(State(state): State<AppState>) -> Json<Value> {
    info!("🖼️ 开始处理内页图片爬取请求");

    // 🗄️ 检查数据库可用性
    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用",
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    // 🔍 获取待处理记录
    let record = match db.get_pending_detail_record().await {
        Ok(Some(record)) => record,
        Ok(None) => {
            info!("📭 没有待处理的记录");
            return Json(serde_json::json!({
                "success": true,
                "message": "没有待处理的记录",
                "timestamp": chrono::Utc::now()
            }));
        }
        Err(e) => {
            error!("❌ 获取待处理记录失败: {}", e);
            return Json(serde_json::json!({
                "success": false,
                "error": format!("获取待处理记录失败: {}", e),
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    let title = record.title.as_deref().unwrap_or("未知标题");
    let url = record.url.as_deref().unwrap_or("");

    if url.is_empty() {
        error!("❌ 记录URL为空: xrid={}", record.xrid);
        return Json(serde_json::json!({
            "success": false,
            "error": "记录URL为空",
            "xrid": record.xrid,
            "timestamp": chrono::Utc::now()
        }));
    }

    info!("🎯 处理记录: xrid={}, title={}", record.xrid, title);

    // 🔍 智能检查：是否已有足够的图片
    match db.check_existing_images(record.xrid as i32).await {
        Ok((existing_count, success_count)) => {
            if existing_count > 0 {
                info!("📊 xrid={} 已有{}张图片({}张成功)", record.xrid, existing_count, success_count);

                // 如果成功图片数量>=30，跳过处理
                if success_count >= 30 {
                    info!("✅ xrid={} 已有足够图片，跳过处理", record.xrid);
                    if let Err(e) = db.update_record_status(record.id, 1).await {
                        warn!("⚠️  更新完成状态失败: {}", e);
                    }
                    return Json(serde_json::json!({
                        "success": true,
                        "reason": "sufficient_images",
                        "xrid": record.xrid,
                        "existingCount": existing_count,
                        "successCount": success_count,
                        "timestamp": chrono::Utc::now()
                    }));
                }

                // 如果图片数量不足，清理后重新爬取
                if success_count < 30 {
                    info!("🔄 xrid={} 图片数量不足，清理后重新爬取", record.xrid);
                    match db.clean_existing_images(record.xrid as i32).await {
                        Ok(deleted_count) => {
                            info!("🗑️  已清理 xrid={} 的{}张旧记录", record.xrid, deleted_count);
                        }
                        Err(e) => {
                            warn!("❌ 清理图片失败: {}", e);
                        }
                    }
                }
            }
        }
        Err(e) => {
            warn!("❌ 检查已有图片失败: {}", e);
        }
    }

    // 🔄 更新状态为处理中 (issave=3)
    if let Err(e) = db.update_record_status(record.id, 3).await {
        error!("❌ 更新状态失败: {}", e);
        return Json(serde_json::json!({
            "success": false,
            "error": format!("更新状态失败: {}", e),
            "timestamp": chrono::Utc::now()
        }));
    }

    // 🕷️ 爬取详情页图片
    match state.crawler_service.get_detail_images(url).await {
        Ok(images) => {
            let image_count = images.len();
            info!("📸 成功获取{}张图片", image_count);

            if images.is_empty() {
                // 没有图片，重置状态
                if let Err(e) = db.update_record_status(record.id, 0).await {
                    warn!("⚠️  重置状态失败: {}", e);
                }

                return Json(serde_json::json!({
                    "success": false,
                    "reason": "no_images_found",
                    "xrid": record.xrid,
                    "message": "未找到图片",
                    "timestamp": chrono::Utc::now()
                }));
            }

            // 💾 保存图片记录到数据库
            match db.batch_create_images(record.xrid as i32, &images).await {
                Ok(saved_count) => {
                    // ✅ 更新状态为完成 (issave=1)
                    if let Err(e) = db.update_record_status(record.id, 1).await {
                        warn!("⚠️  更新完成状态失败: {}", e);
                    }

                    info!("🎉 xrid={} 处理完成: 获取{}张图片，保存{}张",
                          record.xrid, image_count, saved_count);

                    Json(serde_json::json!({
                        "success": true,
                        "xrid": record.xrid,
                        "title": title,
                        "image_count": image_count,
                        "saved_count": saved_count,
                        "performance": {
                            "rust_powered": true,
                            "memory_safe": true,
                            "blazingly_fast": true,
                            "zero_cost_abstractions": true
                        },
                        "timestamp": chrono::Utc::now()
                    }))
                }
                Err(e) => {
                    error!("❌ 保存图片记录失败: {}", e);

                    // 保存失败，重置状态
                    if let Err(reset_err) = db.update_record_status(record.id, 0).await {
                        warn!("⚠️  重置状态失败: {}", reset_err);
                    }

                    Json(serde_json::json!({
                        "success": false,
                        "xrid": record.xrid,
                        "error": format!("保存图片记录失败: {}", e),
                        "image_count": image_count,
                        "timestamp": chrono::Utc::now()
                    }))
                }
            }
        }
        Err(e) => {
            error!("❌ 爬取详情页失败: xrid={}, error={}", record.xrid, e);

            // 爬取失败，重置状态
            if let Err(reset_err) = db.update_record_status(record.id, 0).await {
                warn!("⚠️  重置状态失败: {}", reset_err);
            }

            Json(serde_json::json!({
                "success": false,
                "xrid": record.xrid,
                "error": format!("爬取详情页失败: {}", e),
                "timestamp": chrono::Utc::now()
            }))
        }
    }
}

/// 🖼️ 图片上传处理器 (reurl) - 内页图片上传核心API
///
/// 这是Rust版本的图片上传实现：
/// - 🎯 智能重复检测
/// - 🔒 上传状态管理
/// - 📸 高效图片处理
/// - 💾 批量数据更新
#[axum::debug_handler]
pub async fn reurl(State(state): State<AppState>) -> Json<Value> {
    info!("🖼️ 开始处理图片上传请求");

    // 🗄️ 检查数据库可用性
    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用",
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    // 🔍 获取待处理图片记录
    let record = match db.get_pending_image_record().await {
        Ok(Some(record)) => record,
        Ok(None) => {
            info!("📭 没有待处理的图片记录");
            return Json(serde_json::json!({
                "success": false,
                "message": "no_records",
                "timestamp": chrono::Utc::now()
            }));
        }
        Err(e) => {
            error!("❌ 获取待处理图片记录失败: {}", e);
            return Json(serde_json::json!({
                "success": false,
                "error": format!("获取待处理图片记录失败: {}", e),
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    let ourl = record.ourl.as_deref().unwrap_or("");
    if ourl.is_empty() {
        error!("❌ 图片URL为空: id={}", record.id);
        return Json(serde_json::json!({
            "success": false,
            "error": "图片URL为空",
            "id": record.id,
            "timestamp": chrono::Utc::now()
        }));
    }

    info!("🎯 处理图片: id={}, xrid={}, ourl={}", record.id, record.xrid, ourl);

    // 🔍 检查是否已经处理过（防重复上传）
    match db.check_existing_reurl(ourl).await {
        Ok(Some(existing_reurl)) => {
            info!("♻️  发现重复图片，复用已上传结果: {} -> {}", ourl, existing_reurl);

            if let Err(e) = db.update_image_reurl(record.id, &existing_reurl).await {
                warn!("⚠️  更新重复结果失败: {}", e);
            }

            return Json(serde_json::json!({
                "success": true,
                "reason": "reused_existing",
                "id": record.id,
                "ourl": ourl,
                "reurl": existing_reurl,
                "timestamp": chrono::Utc::now()
            }));
        }
        Ok(None) => {
            // 没有重复，继续处理
        }
        Err(e) => {
            warn!("⚠️  检查重复失败: {}", e);
            // 继续处理，不阻止上传
        }
    }

    // 🔄 标记为处理中
    if let Err(e) = db.update_image_reurl(record.id, "processing").await {
        warn!("⚠️  更新处理状态失败: {}", e);
    }

    // 🚀 创建上传服务并处理图片
    let upload_service = crate::services::UploadService::new();

    // 🌐 构建完整图片URL
    let full_image_url = match upload_service.build_image_url(ourl).await {
        Ok(url) => {
            info!("🌐 完整图片URL: {}", url);
            url
        }
        Err(e) => {
            error!("❌ 构建图片URL失败: {}", e);
            let _ = db.update_image_reurl(record.id, "4040").await;
            return Json(serde_json::json!({
                "success": false,
                "id": record.id,
                "error": format!("构建图片URL失败: {}", e),
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    // 📸 上传图片
    match upload_service.upload_image(&full_image_url).await {
        Ok(upload_results) => {
            let reurl = &upload_results[0].src;

            // 💾 更新数据库
            if let Err(e) = db.update_image_reurl(record.id, reurl).await {
                error!("❌ 更新reurl失败: {}", e);
            }

            info!("🎉 图片上传成功: id={}, {} -> {}", record.id, ourl, reurl);

            Json(serde_json::json!({
                "success": true,
                "id": record.id,
                "xrid": record.xrid,
                "ourl": ourl,
                "reurl": reurl,
                "performance": {
                    "rust_powered": true,
                    "memory_safe": true,
                    "blazingly_fast": true,
                    "zero_cost_abstractions": true
                },
                "timestamp": chrono::Utc::now()
            }))
        }
        Err(e) => {
            error!("❌ 上传图片失败: id={}, error={}", record.id, e);

            // 上传失败，标记为4040
            if let Err(update_err) = db.update_image_reurl(record.id, "4040").await {
                warn!("⚠️  更新失败状态失败: {}", update_err);
            }

            Json(serde_json::json!({
                "success": false,
                "id": record.id,
                "xrid": record.xrid,
                "error": format!("上传图片失败: {}", e),
                "timestamp": chrono::Utc::now()
            }))
        }
    }
}

/// 🎨 封面图片上传处理器 (refmurl) - 封面图片上传核心API
///
/// 这是Rust版本的封面上传实现：
/// - 🎯 智能重复检测
/// - 🔒 上传状态管理
/// - 🖼️ 高效封面处理
/// - 💾 批量数据更新
#[axum::debug_handler]
pub async fn refmurl(State(state): State<AppState>) -> Json<Value> {
    info!("🎨 开始处理封面图片上传请求");

    // 🗄️ 检查数据库可用性
    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用",
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    // 🔍 获取待处理封面记录
    let record = match db.get_pending_cover_record().await {
        Ok(Some(record)) => record,
        Ok(None) => {
            info!("📭 没有待处理的封面记录");
            return Json(serde_json::json!({
                "success": false,
                "message": "no_records",
                "timestamp": chrono::Utc::now()
            }));
        }
        Err(e) => {
            error!("❌ 获取待处理封面记录失败: {}", e);
            return Json(serde_json::json!({
                "success": false,
                "error": format!("获取待处理封面记录失败: {}", e),
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    let fm = record.fm.as_deref().unwrap_or("");
    if fm.is_empty() {
        error!("❌ 封面URL为空: id={}", record.id);
        return Json(serde_json::json!({
            "success": false,
            "error": "封面URL为空",
            "id": record.id,
            "timestamp": chrono::Utc::now()
        }));
    }

    info!("🎯 处理封面: id={}, xrid={}, fm={}", record.id, record.xrid, fm);

    // 🔍 检查是否已经处理过（防重复上传）
    match db.check_existing_refm(fm).await {
        Ok(Some(existing_refm)) => {
            info!("♻️  发现重复封面，复用已上传结果: {} -> {}", fm, existing_refm);

            if let Err(e) = db.update_cover_refm(record.id, &existing_refm).await {
                warn!("⚠️  更新重复结果失败: {}", e);
            }

            return Json(serde_json::json!({
                "success": true,
                "reason": "reused_existing",
                "id": record.id,
                "fm": fm,
                "refm": existing_refm,
                "timestamp": chrono::Utc::now()
            }));
        }
        Ok(None) => {
            // 没有重复，继续处理
        }
        Err(e) => {
            warn!("⚠️  检查重复失败: {}", e);
            // 继续处理，不阻止上传
        }
    }

    // 🔄 标记为处理中
    if let Err(e) = db.update_cover_refm(record.id, "processing").await {
        warn!("⚠️  更新处理状态失败: {}", e);
    }

    // 🚀 创建上传服务并处理封面
    let upload_service = crate::services::UploadService::new();

    // 🌐 构建完整封面URL
    let full_cover_url = match upload_service.build_image_url(fm).await {
        Ok(url) => {
            info!("🌐 完整封面URL: {}", url);
            url
        }
        Err(e) => {
            error!("❌ 构建封面URL失败: {}", e);
            let _ = db.update_cover_refm(record.id, "4040").await;
            return Json(serde_json::json!({
                "success": false,
                "id": record.id,
                "error": format!("构建封面URL失败: {}", e),
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    // 🖼️ 上传封面
    match upload_service.upload_image(&full_cover_url).await {
        Ok(upload_results) => {
            let refm = &upload_results[0].src;

            // 💾 更新数据库
            if let Err(e) = db.update_cover_refm(record.id, refm).await {
                error!("❌ 更新refm失败: {}", e);
            }

            info!("🎉 封面上传成功: id={}, {} -> {}", record.id, fm, refm);

            Json(serde_json::json!({
                "success": true,
                "id": record.id,
                "xrid": record.xrid,
                "fm": fm,
                "refm": refm,
                "performance": {
                    "rust_powered": true,
                    "memory_safe": true,
                    "blazingly_fast": true,
                    "zero_cost_abstractions": true
                },
                "timestamp": chrono::Utc::now()
            }))
        }
        Err(e) => {
            error!("❌ 上传封面失败: id={}, error={}", record.id, e);

            // 上传失败，标记为4040
            if let Err(update_err) = db.update_cover_refm(record.id, "4040").await {
                warn!("⚠️  更新失败状态失败: {}", update_err);
            }

            Json(serde_json::json!({
                "success": false,
                "id": record.id,
                "xrid": record.xrid,
                "error": format!("上传封面失败: {}", e),
                "timestamp": chrono::Utc::now()
            }))
        }
    }
}

/// 🔄 图片上传重试处理器 (reurl/retry) - 解决processing问题的核心API
///
/// 这是Rust版本的重试机制实现：
/// - 🎯 智能错误码过滤
/// - 🔄 批量重试处理
/// - 📊 详细统计报告
/// - 💾 状态重置管理
#[axum::debug_handler]
pub async fn reurl_retry(
    Query(params): Query<HashMap<String, String>>,
    State(state): State<AppState>,
) -> Json<Value> {
    info!("🔄 开始处理图片上传重试请求");

    // 🗄️ 检查数据库可用性
    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用",
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    // 🎯 解析参数
    let codes = params.get("codes")
        .map(|s| s.split(',').map(|s| s.trim().to_string()).collect())
        .unwrap_or_else(|| vec!["4500".to_string(), "4503".to_string(), "4040".to_string()]);

    let limit = params.get("limit")
        .and_then(|s| s.parse::<i64>().ok())
        .unwrap_or(10)
        .min(50); // 最大50条

    info!("🔍 重试参数: codes={:?}, limit={}", codes, limit);

    // 🔍 获取失败的记录
    let failed_records = match db.get_failed_image_records(&codes, limit).await {
        Ok(records) => records,
        Err(e) => {
            error!("❌ 获取失败记录失败: {}", e);
            return Json(serde_json::json!({
                "success": false,
                "error": format!("获取失败记录失败: {}", e),
                "timestamp": chrono::Utc::now()
            }));
        }
    };

    if failed_records.is_empty() {
        info!("📭 没有找到需要重试的失败记录");
        return Json(serde_json::json!({
            "success": true,
            "message": "没有找到需要重试的失败记录",
            "total": 0,
            "processedCount": 0,
            "timestamp": chrono::Utc::now()
        }));
    }

    info!("📋 找到 {} 条失败记录，开始重新处理", failed_records.len());

    // 🚀 创建上传服务
    let upload_service = crate::services::UploadService::new();
    let mut results = Vec::new();
    let mut success_count = 0;

    // 🔄 逐个重试处理
    for record in failed_records {
        info!("🔄 重新处理: id={}, xrid={}", record.id, record.xrid);

        // 重置为NULL，让系统重新处理
        if let Err(e) = db.update_image_reurl(record.id, "processing").await {
            warn!("⚠️  更新处理状态失败: {}", e);
        }

        let ourl = record.ourl.as_deref().unwrap_or("");
        if ourl.is_empty() {
            results.push(serde_json::json!({
                "success": false,
                "id": record.id,
                "error": "图片URL为空"
            }));
            continue;
        }

        // 🌐 构建完整图片URL并上传
        match upload_service.build_image_url(ourl).await {
            Ok(full_url) => {
                match upload_service.upload_image(&full_url).await {
                    Ok(upload_results) => {
                        let reurl = &upload_results[0].src;

                        // 更新数据库
                        if let Err(e) = db.update_image_reurl(record.id, reurl).await {
                            error!("❌ 更新reurl失败: {}", e);
                        }

                        if reurl.starts_with("/file/") {
                            success_count += 1;
                        }

                        results.push(serde_json::json!({
                            "success": reurl.starts_with("/file/"),
                            "id": record.id,
                            "xrid": record.xrid,
                            "ourl": ourl,
                            "reurl": reurl
                        }));

                        info!("✅ 重试成功: id={}, {} -> {}", record.id, ourl, reurl);
                    }
                    Err(e) => {
                        error!("❌ 重试上传失败: id={}, error={}", record.id, e);
                        let _ = db.update_image_reurl(record.id, "4040").await;

                        results.push(serde_json::json!({
                            "success": false,
                            "id": record.id,
                            "error": format!("上传失败: {}", e)
                        }));
                    }
                }
            }
            Err(e) => {
                error!("❌ 构建URL失败: id={}, error={}", record.id, e);
                let _ = db.update_image_reurl(record.id, "4040").await;

                results.push(serde_json::json!({
                    "success": false,
                    "id": record.id,
                    "error": format!("构建URL失败: {}", e)
                }));
            }
        }

        // 延迟避免过快请求
        tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;
    }

    let total = results.len();
    let failure = total - success_count;
    let success_rate = if total > 0 { (success_count as f64 / total as f64 * 100.0).round() } else { 0.0 };

    info!("📊 重试完成: {}条记录, 成功{}条, 失败{}条, 成功率{}%",
          total, success_count, failure, success_rate);

    Json(serde_json::json!({
        "success": true,
        "total": total,
        "processedCount": success_count,
        "successCount": success_count,
        "failureCount": failure,
        "successRate": format!("{}%", success_rate),
        "results": results,
        "performance": {
            "rust_powered": true,
            "memory_safe": true,
            "blazingly_fast": true,
            "zero_cost_abstractions": true
        },
        "timestamp": chrono::Utc::now()
    }))
}

/// 📊 图片上传统计处理器 (reurl/stats)
#[axum::debug_handler]
pub async fn reurl_stats(State(state): State<AppState>) -> Json<Value> {
    info!("📊 获取图片上传统计信息");

    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用"
            }));
        }
    };

    match db.get_reurl_stats().await {
        Ok(stats) => {
            info!("✅ 统计信息获取成功");
            Json(serde_json::json!({
                "success": true,
                "stats": stats,
                "timestamp": chrono::Utc::now()
            }))
        }
        Err(e) => {
            error!("❌ 获取统计信息失败: {}", e);
            Json(serde_json::json!({
                "success": false,
                "error": format!("获取统计信息失败: {}", e)
            }))
        }
    }
}

/// 🔄 图片爬取重置处理器 (getimglist/reset) - 解决processing问题
#[axum::debug_handler]
pub async fn getimglist_reset(State(state): State<AppState>) -> Json<Value> {
    info!("🔄 重置处理中状态的记录");

    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用"
            }));
        }
    };

    match db.reset_processing_records().await {
        Ok(reset_count) => {
            info!("✅ 已重置{}条处理中记录", reset_count);
            Json(serde_json::json!({
                "success": true,
                "resetCount": reset_count,
                "message": format!("已重置{}条处理中记录", reset_count),
                "timestamp": chrono::Utc::now()
            }))
        }
        Err(e) => {
            error!("❌ 重置处理中状态失败: {}", e);
            Json(serde_json::json!({
                "success": false,
                "error": format!("重置处理中状态失败: {}", e)
            }))
        }
    }
}

/// 📊 图片爬取统计处理器 (getimglist/stats)
#[axum::debug_handler]
pub async fn getimglist_stats(State(state): State<AppState>) -> Json<Value> {
    info!("📊 获取图片爬取统计信息");

    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用"
            }));
        }
    };

    match db.get_imglist_stats().await {
        Ok(stats) => {
            info!("✅ 爬取统计信息获取成功");
            Json(serde_json::json!({
                "success": true,
                "stats": stats,
                "timestamp": chrono::Utc::now()
            }))
        }
        Err(e) => {
            error!("❌ 获取爬取统计信息失败: {}", e);
            Json(serde_json::json!({
                "success": false,
                "error": format!("获取爬取统计信息失败: {}", e)
            }))
        }
    }
}

/// 🧹 数据清理处理器 (upnull)
#[axum::debug_handler]
pub async fn upnull(State(state): State<AppState>) -> Json<Value> {
    info!("🧹 开始清理无效数据");

    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用"
            }));
        }
    };

    match db.cleanup_invalid_data().await {
        Ok((cleared_reurl, reset_records)) => {
            info!("🧹 数据清理完成: 清理{}条reurl, 重置{}条记录", cleared_reurl, reset_records);
            Json(serde_json::json!({
                "success": true,
                "clearedReurl": cleared_reurl,
                "resetRecords": reset_records,
                "message": format!("清理{}条reurl, 重置{}条记录", cleared_reurl, reset_records),
                "timestamp": chrono::Utc::now()
            }))
        }
        Err(e) => {
            error!("❌ 数据清理失败: {}", e);
            Json(serde_json::json!({
                "success": false,
                "error": format!("数据清理失败: {}", e)
            }))
        }
    }
}

/// 🔄 重置issave状态处理器 (upsave)
#[axum::debug_handler]
pub async fn upsave(State(state): State<AppState>) -> Json<Value> {
    info!("🔄 重置issave状态");

    let db = match &state.database {
        Some(db) => db,
        None => {
            error!("❌ 数据库不可用");
            return Json(serde_json::json!({
                "success": false,
                "error": "数据库不可用"
            }));
        }
    };

    match db.reset_issave_status().await {
        Ok(updated_count) => {
            info!("✅ 已重置{}条issave状态", updated_count);
            Json(serde_json::json!({
                "success": true,
                "updatedCount": updated_count,
                "message": format!("已重置{}条issave状态", updated_count),
                "timestamp": chrono::Utc::now()
            }))
        }
        Err(e) => {
            error!("❌ 重置issave状态失败: {}", e);
            Json(serde_json::json!({
                "success": false,
                "error": format!("重置issave状态失败: {}", e)
            }))
        }
    }
}
