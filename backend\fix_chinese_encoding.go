package main

import (
	"fmt"
	"log"
	"strings"
	"unicode/utf8"
	"xr-gallery/internal/config"
	"xr-gallery/internal/database"
	"xr-gallery/internal/model"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig(config.GetConfigPath())
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.InitDB(cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 获取数据库实例
	db := database.GetDB()

	// 统计信息
	var totalChecked, fixedCount, noFixNeeded, errorCount int

	// 查询所有记录
	var galleries []model.XR
	if err := db.Find(&galleries).Error; err != nil {
		log.Fatalf("Failed to query galleries: %v", err)
	}

	fmt.Printf("🔍 找到 %d 条记录需要检查\n", len(galleries))

	for i, gallery := range galleries {
		totalChecked++

		// 检测是否需要修复
		if fixedTitle := fixEncodingIfNeeded(gallery.Title); fixedTitle != "" {
			fmt.Printf("🔧 修复记录 ID=%d, XRID=%d\n", gallery.ID, gallery.XRID)
			fmt.Printf("   原始: %s\n", gallery.Title)
			fmt.Printf("   修复: %s\n", fixedTitle)

			// 更新数据库
			if err := db.Model(&gallery).Update("title", fixedTitle).Error; err != nil {
				errorCount++
				fmt.Printf("❌ 修复失败: %v\n", err)
			} else {
				fixedCount++
				fmt.Printf("✅ 修复成功\n")
			}
		} else {
			noFixNeeded++
		}

		// 每100条记录输出一次进度
		if (i+1)%100 == 0 {
			fmt.Printf("📊 进度: %d/%d 已检查, %d 已修复, %d 错误\n",
				totalChecked, len(galleries), fixedCount, errorCount)
		}
	}

	// 输出最终统计
	fmt.Printf("🎯 修复任务完成！\n")
	fmt.Printf("📊 统计结果:\n")
	fmt.Printf("   总检查数: %d\n", totalChecked)
	fmt.Printf("   修复成功: %d\n", fixedCount)
	fmt.Printf("   无需修复: %d\n", noFixNeeded)
	fmt.Printf("   修复失败: %d\n", errorCount)
}

// fixEncodingIfNeeded 检测并修复编码问题
func fixEncodingIfNeeded(input string) string {
	if input == "" {
		return ""
	}

	// 检测是否包含典型的双重UTF-8编码特征
	if !containsDoubleEncodingPatterns(input) {
		return ""
	}

	// 尝试修复双重UTF-8编码
	if fixed := fixDoubleUTF8Encoding(input); fixed != "" {
		// 验证修复结果是否更好
		if isBetterChineseText(fixed, input) {
			return fixed
		}
	}

	return ""
}

// containsDoubleEncodingPatterns 检测双重编码特征
func containsDoubleEncodingPatterns(text string) bool {
	// 常见的双重UTF-8编码特征
	patterns := []string{
		"ç§€", "äºº", "ç½'", "æ¨¡", "ç‰¹", // 秀人网模特
		"æ€§", "æ„Ÿ", "è‰²", "è£…", "æ‰®", // 性感色装扮
		"å†…", "è¡£", "ç§€", "èº«", "æ", // 内衣秀身材
		"è¯±", "æƒ'", "å†™", "çœŸ", "ç…§", // 诱惑写真照
	}

	for _, pattern := range patterns {
		if strings.Contains(text, pattern) {
			return true
		}
	}
	return false
}

// fixDoubleUTF8Encoding 修复双重UTF-8编码
func fixDoubleUTF8Encoding(input string) string {
	// 方法1: 尝试Latin1修复
	if fixed := tryLatin1Fix(input); fixed != "" {
		return fixed
	}

	// 方法2: 尝试GBK修复
	if fixed := tryGBKFix(input); fixed != "" {
		return fixed
	}

	return ""
}

// tryLatin1Fix Latin1修复方法
func tryLatin1Fix(input string) string {
	// 将UTF-8字符串的每个字节重新解释为Latin1字符
	bytes := []byte(input)
	runes := make([]rune, len(bytes))

	for i, b := range bytes {
		runes[i] = rune(b)
	}

	result := string(runes)

	// 验证结果是否是有效的UTF-8
	if utf8.ValidString(result) {
		return result
	}

	return ""
}

// tryGBKFix GBK修复方法
func tryGBKFix(input string) string {
	// 尝试将输入作为GBK解码为UTF-8
	decoder := simplifiedchinese.GBK.NewDecoder()
	result, _, err := transform.String(decoder, input)
	if err != nil {
		return ""
	}

	if utf8.ValidString(result) {
		return result
	}

	return ""
}

// isBetterChineseText 判断修复后的文本是否更好
func isBetterChineseText(fixed, original string) bool {
	fixedChineseCount := countChineseChars(fixed)
	originalChineseCount := countChineseChars(original)

	// 如果修复后的中文字符数量明显增加，认为修复有效
	return fixedChineseCount > originalChineseCount && fixedChineseCount > 5
}

// countChineseChars 统计中文字符数量
func countChineseChars(text string) int {
	count := 0
	for _, r := range text {
		code := int(r)
		// 基本中文字符范围
		if (code >= 0x4E00 && code <= 0x9FFF) ||
			// 中文标点符号
			(code >= 0x3000 && code <= 0x303F) ||
			// 其他中文相关字符
			(code >= 0xFF00 && code <= 0xFFEF) {
			count++
		}
	}
	return count
}

// 测试单个字符串的修复效果
func testSingleString() {
	testStr := "[XiuRenç§€äººç½']No.10550_æ¨¡ç‰¹ZoeæŸšæŸšæ€§æ„Ÿç™½ç‹è£…æ‰®ç™½è‰²ç»'æ¯›æƒ…è¶£å†…è¡£ç§€å‡¹å‡¸èº«æè¯±æƒ'å†™çœŸ79P"

	fmt.Printf("原始字符串: %s\n", testStr)
	fmt.Printf("是否包含双重编码特征: %v\n", containsDoubleEncodingPatterns(testStr))

	if fixed := fixEncodingIfNeeded(testStr); fixed != "" {
		fmt.Printf("修复后: %s\n", fixed)
		fmt.Printf("修复效果更好: %v\n", isBetterChineseText(fixed, testStr))
	} else {
		fmt.Printf("无需修复或修复失败\n")
	}
}
