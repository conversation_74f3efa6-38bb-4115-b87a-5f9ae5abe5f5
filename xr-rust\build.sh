#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Add the required Rust targets
echo "--- Installing required toolchains ---"
rustup target add aarch64-unknown-linux-gnu
rustup target add x86_64-unknown-linux-gnu

# Build for ARM64
echo "--- Building for aarch64-unknown-linux-gnu (native) ---"
cargo build --target aarch64-unknown-linux-gnu --release

# Build for Linux x86_64
echo "--- Building for x86_64-unknown-linux-gnu (cross-compile) ---"
cross build --target x86_64-unknown-linux-gnu --release

# Copy the binaries to the current directory
echo "--- Copying binaries ---"
cp target/aarch64-unknown-linux-gnu/release/xr-crawler ./xr-crawler-arm64
cp target/x86_64-unknown-linux-gnu/release/xr-crawler ./xr-crawler-x64

echo "--- Build complete! ---"
echo "Binaries are available in the current directory: ./xr-crawler-arm64 and ./xr-crawler-x64"
