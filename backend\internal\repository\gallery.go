package repository

import (
	"fmt"
	"xr-gallery/internal/database"
	"xr-gallery/internal/model"

	"gorm.io/gorm"
)

type GalleryRepository struct {
	db *gorm.DB
}

// NewGalleryRepository 创建图库仓库实例
func NewGalleryRepository() *GalleryRepository {
	return &GalleryRepository{
		db: database.GetDB(),
	}
}

// GetGalleryList 获取图库列表
func (r *GalleryRepository) GetGalleryList(page, limit int, sort string) ([]model.GalleryListItem, int64, error) {
	var galleries []model.XR
	var total int64

	// 获取总数 - 只统计xrid大于0的记录
	if err := r.db.Model(&model.XR{}).Where("issave = ? AND xrid > ?", 1, 0).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count galleries: %w", err)
	}

	// 排序逻辑：xrid越大越新，默认降序显示
	orderBy := "xrid DESC"
	if sort == "oldest" {
		orderBy = "xrid ASC"
	}

	// 分页查询 - 使用原生SQL查询，确保xrid不为0
	offset := (page - 1) * limit
	query := fmt.Sprintf(`
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE issave = 1 AND xrid > 0
		ORDER BY %s
		LIMIT %d OFFSET %d
	`, orderBy, limit, offset)

	// 使用原生SQL查询并手动映射结果
	rows, err := r.db.Raw(query).Rows()
	if err != nil {
		return nil, 0, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	galleries = []model.XR{}
	for rows.Next() {
		var gallery model.XR
		if err := rows.Scan(&gallery.ID, &gallery.XRID, &gallery.IsSave, &gallery.FM, &gallery.ReFM, &gallery.Title, &gallery.URL); err != nil {
			return nil, 0, fmt.Errorf("failed to scan row: %w", err)
		}
		galleries = append(galleries, gallery)
	}

	// 转换为响应格式并统计图片数量
	var result []model.GalleryListItem
	for _, gallery := range galleries {
		// 统计该图库的图片数量
		var imageCount int64
		r.db.Model(&model.XRInfo{}).Where("xrid = ?", gallery.XRID).Count(&imageCount)

		item := model.GalleryListItem{
			ID:            gallery.ID,
			XRID:          gallery.XRID,
			Title:         gallery.Title,
			CoverOriginal: gallery.ReFM,
			ImageCount:    int(imageCount),
		}

		// 直接使用原始图片URL（性能更好）
		if gallery.ReFM != "" {
			item.Cover = fmt.Sprintf("https://img1.101616.xyz%s", gallery.ReFM)
		}

		result = append(result, item)
	}

	return result, total, nil
}

// GetGalleryDetail 获取图库详情
func (r *GalleryRepository) GetGalleryDetail(xrid int) (*model.GalleryDetail, error) {
	// 使用原生SQL查询基本信息
	galleryQuery := `
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE xrid = ? AND issave = 1
	`

	rows, err := r.db.Raw(galleryQuery, xrid).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to execute gallery query: %w", err)
	}
	defer rows.Close()

	if !rows.Next() {
		return nil, fmt.Errorf("gallery not found")
	}

	var gallery model.XR
	if err := rows.Scan(&gallery.ID, &gallery.XRID, &gallery.IsSave, &gallery.FM, &gallery.ReFM, &gallery.Title, &gallery.URL); err != nil {
		return nil, fmt.Errorf("failed to scan gallery row: %w", err)
	}

	// 使用原生SQL查询所有图片
	imagesQuery := `
		SELECT id, xrid, ourl, reurl
		FROM xrinfo
		WHERE xrid = ?
		ORDER BY id ASC
	`

	imageRows, err := r.db.Raw(imagesQuery, xrid).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to execute images query: %w", err)
	}
	defer imageRows.Close()

	var images []model.XRInfo
	for imageRows.Next() {
		var img model.XRInfo
		if err := imageRows.Scan(&img.ID, &img.XRID, &img.OURL, &img.ReURL); err != nil {
			return nil, fmt.Errorf("failed to scan image row: %w", err)
		}
		images = append(images, img)
	}

	// 构建图片列表
	var imageItems []model.ImageItem
	for i, img := range images {
		item := model.ImageItem{
			ID:            img.ID,
			OURL:          img.OURL,
			ReURLOriginal: img.ReURL,
			Order:         i + 1,
		}

		// 构建图片代理URL
		if img.ReURL != "" {
			item.ReURL = fmt.Sprintf("https://img1.101616.xyz%s", img.ReURL)
		}

		imageItems = append(imageItems, item)
	}

	// 获取导航信息
	navigation, err := r.GetNavigation(xrid)
	if err != nil {
		return nil, fmt.Errorf("failed to get navigation: %w", err)
	}

	// 构建响应
	info := model.GalleryInfo{
		ID:    gallery.ID,
		XRID:  gallery.XRID,
		Title: gallery.Title,
		URL:   gallery.URL,
	}

	// 构建封面代理URL
	if gallery.ReFM != "" {
		info.Cover = fmt.Sprintf("https://img1.101616.xyz%s", gallery.ReFM)
	}

	detail := &model.GalleryDetail{
		Info:       info,
		Images:     imageItems,
		Navigation: *navigation,
	}

	return detail, nil
}

// GetNavigation 获取导航信息
func (r *GalleryRepository) GetNavigation(currentXrid int) (*model.Navigation, error) {
	var navigation model.Navigation

	// 获取上一套 (xrid更大的，即更新的) - 使用原生SQL
	prevQuery := `
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE xrid > ? AND issave = 1
		ORDER BY xrid ASC
		LIMIT 1
	`

	prevRows, err := r.db.Raw(prevQuery, currentXrid).Rows()
	if err == nil {
		defer prevRows.Close()
		if prevRows.Next() {
			var prevGallery model.XR
			if err := prevRows.Scan(&prevGallery.ID, &prevGallery.XRID, &prevGallery.IsSave, &prevGallery.FM, &prevGallery.ReFM, &prevGallery.Title, &prevGallery.URL); err == nil {
				navigation.Prev = &model.NavigationItem{
					XRID:  prevGallery.XRID,
					Title: prevGallery.Title,
				}
				if prevGallery.ReFM != "" {
					navigation.Prev.Cover = fmt.Sprintf("https://img1.101616.xyz%s", prevGallery.ReFM)
				}
			}
		}
	}

	// 获取下一套 (xrid更小的，即更旧的) - 使用原生SQL
	nextQuery := `
		SELECT id, xrid, issave, fm, refm, title, url
		FROM xr
		WHERE xrid < ? AND issave = 1
		ORDER BY xrid DESC
		LIMIT 1
	`

	nextRows, err := r.db.Raw(nextQuery, currentXrid).Rows()
	if err == nil {
		defer nextRows.Close()
		if nextRows.Next() {
			var nextGallery model.XR
			if err := nextRows.Scan(&nextGallery.ID, &nextGallery.XRID, &nextGallery.IsSave, &nextGallery.FM, &nextGallery.ReFM, &nextGallery.Title, &nextGallery.URL); err == nil {
				navigation.Next = &model.NavigationItem{
					XRID:  nextGallery.XRID,
					Title: nextGallery.Title,
				}
				if nextGallery.ReFM != "" {
					navigation.Next.Cover = fmt.Sprintf("https://img1.101616.xyz%s", nextGallery.ReFM)
				}
			}
		}
	}

	return &navigation, nil
}
