@echo off
echo ========================================
echo XR Gallery 开发环境启动脚本
echo ========================================

echo.
echo [1/4] 清理端口占用...

REM 杀掉3331端口进程（前端）
echo 清理前端端口 3331...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3331') do (
    echo 杀掉进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

REM 杀掉3332端口进程（后端）
echo 清理后端端口 3332...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3332') do (
    echo 杀掉进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo 端口清理完成！

echo.
echo [2/4] 启动后端服务 (Go:3332)...
cd backend
start "XR-Backend" cmd /k "go run cmd/main.go"
cd ..

echo 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo.
echo [3/4] 启动前端服务 (Vue:3331)...
cd frontend
start "XR-Frontend" cmd /k "pnpm dev"
cd ..

echo.
echo [4/4] 启动完成！
echo ========================================
echo 前端地址: http://localhost:3331
echo 后端地址: http://localhost:3332
echo API测试: http://localhost:3331/api/gallery/list?limit=1
echo ========================================
echo.
echo 按任意键退出...
pause >nul
