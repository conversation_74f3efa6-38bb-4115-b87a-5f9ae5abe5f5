{"rustc": 3926191382657067107, "features": "[\"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 11876527447619405325, "path": 13823013089657425784, "deps": [[1906322745568073236, "pin_project_lite", false, 10639121882622035872], [7620660491849607393, "futures_core", false, 11963451606806247311], [12944427623413450645, "tokio", false, 15696514691787130065], [15932120279885307830, "memchr", false, 9802535636153823867], [17772299992546037086, "flate2", false, 10900020528393154259]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-b2523ff206ea6fcc\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}