#!/bin/bash
# XR-Crawler 构建脚本 (Bash)
# 支持本地和跨平台构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# 默认参数
TARGET="native"
RELEASE=false
CLEAN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            echo "XR-Crawler 构建脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  -t, --target TARGET   目标平台 (native|windows|linux|arm64|all)"
            echo "  -r, --release         发布模式构建"
            echo "  -c, --clean           清理构建目录"
            echo "  -h, --help            显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 --target linux --release"
            echo "  $0 --target all --clean"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 未知参数: $1${NC}"
            exit 1
            ;;
    esac
done

echo -e "${GREEN}🚀 XR-Crawler 构建脚本${NC}"
echo -e "${YELLOW}目标平台: $TARGET${NC}"

# 清理构建目录
if [ "$CLEAN" = true ]; then
    echo -e "${BLUE}🧹 清理构建目录...${NC}"
    if [ -d "target" ]; then
        rm -rf target
    fi
fi

# 设置构建参数
BUILD_ARGS=()
if [ "$RELEASE" = true ]; then
    BUILD_ARGS+=(--release)
    echo -e "${GREEN}📦 发布模式构建${NC}"
else
    echo -e "${YELLOW}🔧 调试模式构建${NC}"
fi

# 检查并安装 cross 工具
check_cross() {
    if ! command -v cross &> /dev/null; then
        echo -e "${YELLOW}📦 安装 cross 工具...${NC}"
        cargo install cross --git https://github.com/cross-rs/cross
    fi
}

# 根据目标平台选择构建方式
case "${TARGET,,}" in
    "native")
        echo -e "${BLUE}🏠 本地平台构建...${NC}"
        cargo build "${BUILD_ARGS[@]}"
        ;;
    "windows")
        echo -e "${BLUE}🪟 Windows x64 构建...${NC}"
        BUILD_ARGS+=(--target x86_64-pc-windows-msvc)
        
        # 在非 Windows 系统上需要 cross
        if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "cygwin" ]]; then
            check_cross
            cross build "${BUILD_ARGS[@]}"
        else
            cargo build "${BUILD_ARGS[@]}"
        fi
        ;;
    "linux")
        echo -e "${BLUE}🐧 Linux x64 构建...${NC}"
        BUILD_ARGS+=(--target x86_64-unknown-linux-gnu)
        
        # 在非 Linux 系统上需要 cross
        if [[ "$OSTYPE" != "linux-gnu"* ]]; then
            check_cross
            cross build "${BUILD_ARGS[@]}"
        else
            cargo build "${BUILD_ARGS[@]}"
        fi
        ;;
    "arm64")
        echo -e "${BLUE}💪 Linux ARM64 构建...${NC}"
        BUILD_ARGS+=(--target aarch64-unknown-linux-gnu)
        check_cross
        cross build "${BUILD_ARGS[@]}"
        ;;
    "all")
        echo -e "${BLUE}🌍 全平台构建...${NC}"
        check_cross
        
        # Windows
        echo -e "${CYAN}🪟 构建 Windows x64...${NC}"
        if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
            cargo build "${BUILD_ARGS[@]}" --target x86_64-pc-windows-msvc
        else
            cross build "${BUILD_ARGS[@]}" --target x86_64-pc-windows-msvc
        fi
        
        # Linux x64
        echo -e "${CYAN}🐧 构建 Linux x64...${NC}"
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            cargo build "${BUILD_ARGS[@]}" --target x86_64-unknown-linux-gnu
        else
            cross build "${BUILD_ARGS[@]}" --target x86_64-unknown-linux-gnu
        fi
        
        # Linux ARM64
        echo -e "${CYAN}💪 构建 Linux ARM64...${NC}"
        cross build "${BUILD_ARGS[@]}" --target aarch64-unknown-linux-gnu
        ;;
    *)
        echo -e "${RED}❌ 不支持的目标平台: $TARGET${NC}"
        echo -e "${YELLOW}支持的平台: native, windows, linux, arm64, all${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}✅ 构建完成!${NC}"

# 显示构建产物
if [ "$RELEASE" = true ]; then
    BUILD_DIR="target/release"
else
    BUILD_DIR="target/debug"
fi

if [ -d "$BUILD_DIR" ]; then
    echo -e "${BLUE}📁 构建产物:${NC}"
    find "$BUILD_DIR" -name "xr-crawler*" -type f | while read -r file; do
        echo -e "  ${GRAY}- $(basename "$file")${NC}"
    done
fi
