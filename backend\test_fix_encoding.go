package main

import (
	"fmt"
	"strings"
	"unicode/utf8"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

func main() {
	// 测试字符串 - 从API响应中获取的实际乱码数据
	testStr := "[XiuRenç§€äººç½']No.10550_æ¨¡ç‰¹ZoeæŸšæŸšæ€§æ„Ÿç™½ç‹è£…æ‰®ç™½è‰²ç»'æ¯›æƒ…è¶£å†…è¡£ç§€å‡¹å‡¸èº«æè¯±æƒ'å†™çœŸ79P"
	
	fmt.Printf("🔍 测试字符串修复\n")
	fmt.Printf("原始: %s\n", testStr)
	fmt.Printf("长度: %d 字节, %d 字符\n", len(testStr), utf8.RuneCountInString(testStr))
	fmt.Printf("是否有效UTF-8: %v\n", utf8.ValidString(testStr))
	fmt.Printf("包含双重编码特征: %v\n", containsDoubleEncodingPatterns(testStr))
	
	fmt.Printf("\n🔧 尝试各种修复方法:\n")
	
	// 方法1: Latin1修复
	if fixed1 := tryLatin1Fix(testStr); fixed1 != "" {
		fmt.Printf("Latin1修复: %s\n", fixed1)
		fmt.Printf("中文字符数: %d\n", countChineseChars(fixed1))
	} else {
		fmt.Printf("Latin1修复: 失败\n")
	}
	
	// 方法2: GBK修复
	if fixed2 := tryGBKFix(testStr); fixed2 != "" {
		fmt.Printf("GBK修复: %s\n", fixed2)
		fmt.Printf("中文字符数: %d\n", countChineseChars(fixed2))
	} else {
		fmt.Printf("GBK修复: 失败\n")
	}
	
	// 方法3: 字节重新解释
	if fixed3 := tryByteReinterpretation(testStr); fixed3 != "" {
		fmt.Printf("字节重新解释: %s\n", fixed3)
		fmt.Printf("中文字符数: %d\n", countChineseChars(fixed3))
	} else {
		fmt.Printf("字节重新解释: 失败\n")
	}
	
	// 方法4: 手动双重UTF-8修复
	if fixed4 := tryManualDoubleUTF8Fix(testStr); fixed4 != "" {
		fmt.Printf("手动双重UTF-8修复: %s\n", fixed4)
		fmt.Printf("中文字符数: %d\n", countChineseChars(fixed4))
	} else {
		fmt.Printf("手动双重UTF-8修复: 失败\n")
	}
}

// containsDoubleEncodingPatterns 检测双重编码特征
func containsDoubleEncodingPatterns(text string) bool {
	patterns := []string{
		"ç§€", "äºº", "ç½'", "æ¨¡", "ç‰¹", // 秀人网模特
		"æ€§", "æ„Ÿ", "è‰²", "è£…", "æ‰®", // 性感色装扮
		"å†…", "è¡£", "ç§€", "èº«", "æ", // 内衣秀身材
		"è¯±", "æƒ'", "å†™", "çœŸ", "ç…§", // 诱惑写真照
	}

	for _, pattern := range patterns {
		if strings.Contains(text, pattern) {
			return true
		}
	}
	return false
}

// tryLatin1Fix Latin1修复方法
func tryLatin1Fix(input string) string {
	bytes := []byte(input)
	runes := make([]rune, len(bytes))
	
	for i, b := range bytes {
		runes[i] = rune(b)
	}
	
	result := string(runes)
	
	if utf8.ValidString(result) && countChineseChars(result) > countChineseChars(input) {
		return result
	}
	
	return ""
}

// tryGBKFix GBK修复方法
func tryGBKFix(input string) string {
	decoder := simplifiedchinese.GBK.NewDecoder()
	result, _, err := transform.String(decoder, input)
	if err != nil {
		return ""
	}
	
	if utf8.ValidString(result) && countChineseChars(result) > countChineseChars(input) {
		return result
	}
	
	return ""
}

// tryByteReinterpretation 字节重新解释方法
func tryByteReinterpretation(input string) string {
	// 将UTF-8字符串转换为字节数组，然后重新解释
	bytes := []byte(input)
	
	// 尝试将字节数组重新解释为UTF-8
	if utf8.Valid(bytes) {
		// 尝试不同的字节组合
		var result strings.Builder
		i := 0
		for i < len(bytes) {
			// 尝试单字节
			if bytes[i] < 128 {
				result.WriteByte(bytes[i])
				i++
				continue
			}
			
			// 尝试多字节UTF-8序列
			r, size := utf8.DecodeRune(bytes[i:])
			if r != utf8.RuneError && size > 1 {
				result.WriteRune(r)
				i += size
			} else {
				result.WriteByte(bytes[i])
				i++
			}
		}
		
		resultStr := result.String()
		if countChineseChars(resultStr) > countChineseChars(input) {
			return resultStr
		}
	}
	
	return ""
}

// tryManualDoubleUTF8Fix 手动双重UTF-8修复
func tryManualDoubleUTF8Fix(input string) string {
	// 常见的双重编码映射
	replacements := map[string]string{
		"ç§€äººç½'": "秀人网",
		"æ¨¡ç‰¹": "模特",
		"æ€§æ„Ÿ": "性感",
		"è‰²": "色",
		"è£…æ‰®": "装扮",
		"å†…è¡£": "内衣",
		"ç§€": "秀",
		"èº«æ": "身材",
		"è¯±æƒ'": "诱惑",
		"å†™çœŸ": "写真",
		"æŸšæŸš": "柚柚",
		"ç™½": "白",
		"ç‹": "狐",
		"ç»'æ¯›": "绒毛",
		"æƒ…è¶£": "情趣",
		"å‡¹å‡¸": "凹凸",
	}
	
	result := input
	for encoded, decoded := range replacements {
		result = strings.ReplaceAll(result, encoded, decoded)
	}
	
	if result != input && countChineseChars(result) > countChineseChars(input) {
		return result
	}
	
	return ""
}

// countChineseChars 统计中文字符数量
func countChineseChars(text string) int {
	count := 0
	for _, r := range text {
		code := int(r)
		if (code >= 0x4E00 && code <= 0x9FFF) ||
			(code >= 0x3000 && code <= 0x303F) ||
			(code >= 0xFF00 && code <= 0xFFEF) {
			count++
		}
	}
	return count
}
